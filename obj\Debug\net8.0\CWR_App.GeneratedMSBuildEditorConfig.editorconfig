is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = CWR_App
build_property.RootNamespace = CWR_App
build_property.ProjectDir = C:\Users\<USER>\Desktop\Mabchour\CWR_App\CWR_App\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\Mabchour\CWR_App\CWR_App
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Layout/Sidebar.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcU2lkZWJhci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/ContratsPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb250cmF0c1BhZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/DevisPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEZXZpc1BhZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/FacturationPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xGYWN0dXJhdGlvblBhZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/PVPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQVlBhZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/RecherchePage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xSZWNoZXJjaGVQYWdlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/SinistresPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTaW5pc3RyZXNQYWdlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/SuiviParcPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTdWl2aVBhcmNQYWdlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/VehiclesPage.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xWZWhpY2xlc1BhZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Pages/WafaDashboard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXYWZhRGFzaGJvYXJkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Mabchour/CWR_App/CWR_App/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-anuzff0wnj
