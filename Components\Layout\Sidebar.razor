@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Buttons
@using Microsoft.AspNetCore.Components
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<!-- Mobile floating hamburger button -->
@if (IsMobile && !IsOpen)
{
    <div class="mobile-hamburger-btn @(IsTransitioning ? "transitioning" : "")" @onclick="async () => await ToggleSidebar()">
        <span class="bi bi-list"></span>
        @if (IsTransitioning)
        {
            <div class="transition-indicator"></div>
        }
    </div>
}

<SfSidebar @ref="SidebarObj"
           Width="@(IsMobile ? "280px" : "250px")"
           DockSize="60px"
           Type="@(IsMobile ? SidebarType.Over : SidebarType.Push)"
           Position="SidebarPosition.Left"
           IsOpen="IsOpen"
           EnableDock="!IsMobile"
           ShowBackdrop="IsMobile">
    <ChildContent>
        <!-- Sidebar Header with <PERSON><PERSON> and Toggle -->
        <div class="sidebar-header">
            <div class="logo-section">
                @if (IsOpen)
                {
                    <img src="/images/new_logo.png" alt="Wafa LLD" class="company-logo" />
                    <span class="company-text">WAFA LLD</span>
                }
            </div>
            <!-- Always show toggle button in sidebar -->
            <button class="toggle-btn" @onclick="async () => await ToggleSidebar()">
                <span class="e-icons e-menu"></span>
            </button>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-menu">
            @foreach (var item in MenuItems)
            {
                <div class="menu-item @(item.IsActive ? "active" : "")"
                     @onclick="async () => await NavigateToPage(item)">
                    <div class="menu-icon">
                        <span class="@item.IconClass"></span>
                    </div>
                    @if (IsOpen)
                    {
                        <div class="menu-text">
                            <span>@item.Text</span>
                        </div>
                    }
                </div>
            }
        </div>
    </ChildContent>
</SfSidebar>

@code {
    private SfSidebar? SidebarObj;
    private bool IsOpen = true; // Start open by default (desktop behavior)
    private string CurrentPage = "vue-statistique";
    private bool IsMobile = false;
    private bool IsTransitioning = false;
    private bool DesktopSidebarWasOpen = true; // Remember desktop state
    private bool MobileSidebarWasOpen = false; // Remember mobile state

    private List<MenuItem> MenuItems = new();

    protected override async Task OnInitializedAsync()
    {
        InitializeMenuItems();
        SetCurrentPageFromUrl();
        Navigation.LocationChanged += OnLocationChanged;

        // Check if mobile and set initial state
        await CheckIfMobile();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var previousMobile = IsMobile;
            var previousOpen = IsOpen;

            await CheckIfMobile();
            await JSRuntime.InvokeVoidAsync("addResizeListener", DotNetObjectReference.Create(this));

            // Force re-render if state changed
            if (previousMobile != IsMobile || previousOpen != IsOpen)
            {
                StateHasChanged();
            }
        }
    }

    [JSInvokable]
    public async Task OnWindowResize()
    {
        var wasMobile = IsMobile;
        var wasOpen = IsOpen;

        // Start transition
        IsTransitioning = true;
        StateHasChanged();

        await CheckIfMobile();

        // Enhanced mode switching logic
        if (!wasMobile && IsMobile)
        {
            // Switching from desktop to mobile
            DesktopSidebarWasOpen = wasOpen; // Remember desktop state
            IsOpen = MobileSidebarWasOpen; // Restore mobile preference

            // Add smooth transition delay
            await Task.Delay(150);
            await JSRuntime.InvokeVoidAsync("console.log", "Switched to mobile mode");
        }
        else if (wasMobile && !IsMobile)
        {
            // Switching from mobile to desktop
            MobileSidebarWasOpen = wasOpen; // Remember mobile state
            IsOpen = DesktopSidebarWasOpen; // Restore desktop preference

            // Add smooth transition delay
            await Task.Delay(150);
            await JSRuntime.InvokeVoidAsync("console.log", "Switched to desktop mode");
        }

        // End transition
        IsTransitioning = false;
        StateHasChanged();

        // Trigger content layout adjustment
        await JSRuntime.InvokeVoidAsync("eval", $"window.dispatchEvent(new CustomEvent('modeChanged', {{ detail: {{ isMobile: {IsMobile.ToString().ToLower()}, isOpen: {IsOpen.ToString().ToLower()} }} }}))");
    }

    private async Task CheckIfMobile()
    {
        try
        {
            var newIsMobile = await JSRuntime.InvokeAsync<bool>("eval", "window.innerWidth <= 768");
            var wasFirstCheck = IsMobile == false && newIsMobile == false; // Initial load detection

            IsMobile = newIsMobile;

            // Set initial state based on device type (only on first load)
            if (wasFirstCheck)
            {
                if (IsMobile)
                {
                    IsOpen = MobileSidebarWasOpen; // Use saved mobile preference
                }
                else
                {
                    IsOpen = DesktopSidebarWasOpen; // Use saved desktop preference
                }
            }
        }
        catch
        {
            // Fallback if JS is not available - assume desktop
            IsMobile = false;
            IsOpen = DesktopSidebarWasOpen;
        }
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        SetCurrentPageFromUrl();
        StateHasChanged();
    }

    private void SetCurrentPageFromUrl()
    {
        var uri = new Uri(Navigation.Uri);
        var path = uri.AbsolutePath.ToLower();

        CurrentPage = path switch
        {
            "/dashboard" or "/wafa-dashboard" or "/" => "vue-statistique",
            "/vehicules" => "vehicules-parc",
            "/contrats" => "contrats-cours",
            "/facturation" => "suivi-facturation",
            "/suivi-parc" => "suivi-parc",
            "/sinistres" => "sinistres",
            "/pv" => "pv",
            "/devis" => "devis-ld",
            "/recherche" => "recherche-parc",
            _ => "vue-statistique"
        };

        // Update active state based on current page
        foreach (var item in MenuItems)
        {
            item.IsActive = item.Id == CurrentPage;
        }
    }

    private void InitializeMenuItems()
    {
        MenuItems = new List<MenuItem>
        {
            new MenuItem { Id = "vue-statistique", Text = "Vue statistique", IconClass = "bi bi-bar-chart", IsActive = true },
            new MenuItem { Id = "vehicules-parc", Text = "Véhicules en parc", IconClass = "bi bi-car-front", IsActive = false },
            new MenuItem { Id = "contrats-cours", Text = "Contrats en cours", IconClass = "bi bi-file-text", IsActive = false },
            new MenuItem { Id = "suivi-facturation", Text = "Suivi facturation", IconClass = "bi bi-receipt", IsActive = false },
            new MenuItem { Id = "suivi-parc", Text = "Suivi parc", IconClass = "bi bi-folder2-open", IsActive = false },
            new MenuItem { Id = "sinistres", Text = "Sinistres", IconClass = "bi bi-exclamation-triangle", IsActive = false },
            new MenuItem { Id = "pv", Text = "P.V.", IconClass = "bi bi-clipboard", IsActive = false },
            new MenuItem { Id = "devis-ld", Text = "Devis LD en cours", IconClass = "bi bi-file-earmark-text", IsActive = false },
            new MenuItem { Id = "recherche-parc", Text = "Recherche parc", IconClass = "bi bi-search", IsActive = false }
        };
    }

    private async Task ToggleSidebar()
    {
        IsOpen = !IsOpen;

        // Remember user preference for current mode
        if (IsMobile)
        {
            MobileSidebarWasOpen = IsOpen;
        }
        else
        {
            DesktopSidebarWasOpen = IsOpen;
        }

        StateHasChanged();

        // Trigger smooth animation and content adjustment
        await JSRuntime.InvokeVoidAsync("eval", $"window.dispatchEvent(new CustomEvent('sidebarToggled', {{ detail: {{ isMobile: {IsMobile.ToString().ToLower()}, isOpen: {IsOpen.ToString().ToLower()} }} }}))");
    }

    private async Task NavigateToPage(MenuItem menuItem)
    {
        // Update active state
        foreach (var item in MenuItems)
        {
            item.IsActive = item.Id == menuItem.Id;
        }

        // Navigate to the appropriate page
        var route = GetRouteForMenuItem(menuItem.Id);
        Navigation.NavigateTo(route);

        // Close sidebar on mobile after navigation
        if (IsMobile && IsOpen)
        {
            IsOpen = false;
            await Task.Delay(100); // Small delay for smooth animation
        }

        StateHasChanged();
    }

    private string GetRouteForMenuItem(string menuId)
    {
        return menuId switch
        {
            "vue-statistique" => "/dashboard",
            "vehicules-parc" => "/vehicules",
            "contrats-cours" => "/contrats",
            "suivi-facturation" => "/facturation",
            "suivi-parc" => "/suivi-parc",
            "sinistres" => "/sinistres",
            "pv" => "/pv",
            "devis-ld" => "/devis",
            "recherche-parc" => "/recherche",
            _ => "/dashboard"
        };
    }





    public class MenuItem
    {
        public string Id { get; set; } = "";
        public string Text { get; set; } = "";
        public string IconClass { get; set; } = "";
        public bool IsActive { get; set; } = false;
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }
}

<style>
    @@import url('_content/Syncfusion.Blazor.Themes/bootstrap5.css');

    :root {
        --wafa-orange: #EA7854;
        --sidebar-width: 250px;
        --sidebar-collapsed-width: 60px;
    }

    .e-sidebar {
        background: #ffffff !important;
        border-right: 1px solid #e0e0e0 !important;
        display: flex !important;
        flex-direction: column !important;
        height: 100vh !important;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
    }

    .e-sidebar .e-sidebar-item {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .sidebar-header {
        padding: 20px 15px;
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e0e0e0;
        height: 80px;
        background: #ffffff;
    }

    .logo-section {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-start;
        gap: 10px;
    }

    .company-logo {
        height: 48px;
        width: auto;
        object-fit: contain;
        flex-shrink: 0;
    }

    .company-text {
        font-size: 20px;
        font-weight: 700;
        color: #000;
        white-space: nowrap;
    }

    .toggle-btn {
        background: none;
        border: 1px solid #ddd;
        color: #666;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        display: flex !important;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;
    }

    .toggle-btn:hover {
        background: #f5f5f5;
        border-color: #ccc;
    }

    /* Mobile floating hamburger button */
    .mobile-hamburger-btn {
        position: fixed;
        top: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        background: var(--wafa-orange);
        color: white;
        border: none;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1003;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        font-size: 24px;
    }

    .mobile-hamburger-btn:hover {
        background: var(--wafa-brown);
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .mobile-hamburger-btn:active {
        transform: scale(0.95);
    }

    /* Transition states */
    .mobile-hamburger-btn.transitioning {
        opacity: 0.7;
        pointer-events: none;
    }

    .transition-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Desktop styles */
    @@media (min-width: 769px) {
        .mobile-hamburger-btn {
            display: none !important;
        }

        /* Ensure desktop toggle button is always visible */
        .toggle-btn {
            display: flex !important;
            background: none;
            border: 1px solid #ddd;
            color: #666;
            width: 32px;
            height: 32px;
        }

        .toggle-btn:hover {
            background: #f5f5f5;
            border-color: #ccc;
            transform: none;
        }

        /* Ensure sidebar header is always visible on desktop */
        .sidebar-header {
            display: flex !important;
            padding: 20px 15px;
            height: 80px;
            background: #ffffff;
        }

        /* Ensure logo section is visible when sidebar is open */
        .logo-section {
            display: flex !important;
        }

        /* Ensure sidebar is visible and properly positioned on desktop */
        .e-sidebar {
            position: static !important;
            height: 100vh !important;
            display: block !important;
        }

        /* Ensure sidebar content is visible */
        .sidebar-menu {
            display: block !important;
        }
    }

    /* Enhanced mobile hamburger menu */
    @@media (max-width: 768px) {
        .toggle-btn {
            width: 40px;
            height: 40px;
            border: 2px solid var(--wafa-orange);
            color: var(--wafa-orange);
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex !important;
            align-items: center;
            justify-content: center;
        }

        .toggle-btn:hover {
            background: var(--wafa-orange);
            color: white;
            transform: scale(1.05);
        }

        .sidebar-header {
            padding: 15px;
            height: 70px;
        }

        /* Mobile menu items */
        .menu-item {
            padding: 16px 20px;
            font-size: 16px;
        }

        .menu-icon {
            width: 28px;
            height: 28px;
            font-size: 20px;
            margin-right: 16px;
        }

        /* Ensure sidebar takes full height on mobile */
        .e-sidebar {
            height: 100vh !important;
            width: 280px !important;
        }

        /* Better touch targets */
        .menu-text {
            font-size: 16px;
            font-weight: 500;
        }
    }

    .sidebar-menu {
        flex: 1;
        padding: 10px 0;
        overflow-y: auto;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        color: #333;
        text-decoration: none;
        border-left: 3px solid transparent;
    }

    .menu-item:hover {
        background: #f5f5f5;
    }

    .menu-item.active {
        background: #fff5f0;
        border-left-color: var(--wafa-orange);
        color: var(--wafa-orange);
    }

    .menu-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-right: 12px;
        flex-shrink: 0;
        color: inherit;
    }

    .menu-icon span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 18px;
    }

    /* Bootstrap Icons styling */
    .menu-icon .bi {
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

    .collapsed .menu-icon {
        margin-right: 0;
        width: 30px;
        height: 30px;
        font-size: 20px;
    }

    .menu-text {
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
    }

    .collapsed .menu-text {
        display: none;
    }

    /* Scrollbar */
    .sidebar-menu::-webkit-scrollbar {
        width: 3px;
    }

    .sidebar-menu::-webkit-scrollbar-track {
        background: transparent;
    }

    .sidebar-menu::-webkit-scrollbar-thumb {
        background: #ddd;
        border-radius: 2px;
    }
</style>
