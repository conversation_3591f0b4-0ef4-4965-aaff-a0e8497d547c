{"version": 2, "dgSpecHash": "awBulmAL0q8=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\CWR_App.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\8.0.15\\microsoft.aspnetcore.authorization.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\8.0.15\\microsoft.aspnetcore.components.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\8.0.15\\microsoft.aspnetcore.components.analyzers.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\8.0.15\\microsoft.aspnetcore.components.forms.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\8.0.15\\microsoft.aspnetcore.components.web.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\8.0.15\\microsoft.aspnetcore.metadata.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.3\\microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\8.0.15\\microsoft.jsinterop.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.41\\syncfusion.blazor.buttons.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.41\\syncfusion.blazor.calendars.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\30.1.41\\syncfusion.blazor.charts.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.41\\syncfusion.blazor.core.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.41\\syncfusion.blazor.data.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.datavizcommon\\30.1.41\\syncfusion.blazor.datavizcommon.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.41\\syncfusion.blazor.dropdowns.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.41\\syncfusion.blazor.grid.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.41\\syncfusion.blazor.inputs.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.41\\syncfusion.blazor.lists.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.41\\syncfusion.blazor.navigations.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.41\\syncfusion.blazor.notifications.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.41\\syncfusion.blazor.popups.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.progressbar\\30.1.41\\syncfusion.blazor.progressbar.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.41\\syncfusion.blazor.spinner.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.41\\syncfusion.blazor.splitbuttons.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.41\\syncfusion.blazor.themes.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.excelexport.net.core\\30.1.41\\syncfusion.excelexport.net.core.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\30.1.41\\syncfusion.licensing.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.pdfexport.net.core\\30.1.41\\syncfusion.pdfexport.net.core.30.1.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512"], "logs": []}