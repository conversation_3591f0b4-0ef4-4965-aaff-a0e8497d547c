{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "CWR_App.99334w241v.styles.css", "AssetFile": "CWR_App.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4179"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w6guGcqAOiTNYYnLT3GTUr6HqbeiRwL+fhMXKMcmhIE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 11:18:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99334w241v"}, {"Name": "integrity", "Value": "sha256-w6guGcqAOiTNYYnLT3GTUr6HqbeiRwL+fhMXKMcmhIE="}, {"Name": "label", "Value": "CWR_App.styles.css"}]}, {"Route": "CWR_App.styles.css", "AssetFile": "CWR_App.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4179"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"w6guGcqAOiTNYYnLT3GTUr6HqbeiRwL+fhMXKMcmhIE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 11:18:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w6guGcqAOiTNYYnLT3GTUr6HqbeiRwL+fhMXKMcmhIE="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.0ir6ibxc9a.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ir6ibxc9a"}, {"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.d2wfjv2vus.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2wfjv2vus"}, {"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.m5nz426d3u.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m5nz426d3u"}, {"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.r9k4j69dlg.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r9k4j69dlg"}, {"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.8d7q9eto80.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8d7q9eto80"}, {"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.xskb1o7vx7.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xskb1o7vx7"}, {"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Charts/scripts/sf-accumulation-chart.min.4e32rghbu0.js", "AssetFile": "_content/Syncfusion.Blazor.Charts/scripts/sf-accumulation-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VHdVfOXuFsqXYhXhh7kG5Jvej3xnf/nVqSKIGOB0a7E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4e32rghbu0"}, {"Name": "integrity", "Value": "sha256-VHdVfOXuFsqXYhXhh7kG5Jvej3xnf/nVqSKIGOB0a7E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Charts/scripts/sf-accumulation-chart.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Charts/scripts/sf-accumulation-chart.min.js", "AssetFile": "_content/Syncfusion.Blazor.Charts/scripts/sf-accumulation-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22397"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VHdVfOXuFsqXYhXhh7kG5Jvej3xnf/nVqSKIGOB0a7E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHdVfOXuFsqXYhXhh7kG5Jvej3xnf/nVqSKIGOB0a7E="}]}, {"Route": "_content/Syncfusion.Blazor.Charts/scripts/sf-chart.min.js", "AssetFile": "_content/Syncfusion.Blazor.Charts/scripts/sf-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "243638"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MlfySU1xV5g2B2hwyPZHrqUObwz/ieHt1us7y6Uz0ZU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MlfySU1xV5g2B2hwyPZHrqUObwz/ieHt1us7y6Uz0ZU="}]}, {"Route": "_content/Syncfusion.Blazor.Charts/scripts/sf-chart.min.qil9qevh3h.js", "AssetFile": "_content/Syncfusion.Blazor.Charts/scripts/sf-chart.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "243638"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MlfySU1xV5g2B2hwyPZHrqUObwz/ieHt1us7y6Uz0ZU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qil9qevh3h"}, {"Name": "integrity", "Value": "sha256-MlfySU1xV5g2B2hwyPZHrqUObwz/ieHt1us7y6Uz0ZU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Charts/scripts/sf-chart.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.buyzacakm8.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "buyzacakm8"}, {"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.mzeb2kh2gi.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mzeb2kh2gi"}, {"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.36gmxgyha1.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"paN3TtBGbqOhjUIxMbnaJB+V33WgKzOXm5k47yX+It8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "36gmxgyha1"}, {"Name": "integrity", "Value": "sha256-paN3TtBGbqOhjUIxMbnaJB+V33WgKzOXm5k47yX+It8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9393"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"paN3TtBGbqOhjUIxMbnaJB+V33WgKzOXm5k47yX+It8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-paN3TtBGbqOhjUIxMbnaJB+V33WgKzOXm5k47yX+It8="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.1jog2vdvqp.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jog2vdvqp"}, {"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.invc3awok9.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "248131"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wol/L08BZMXBVH1QM2xYeEOhBiYKyuw9rbG7I3jRyV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "invc3awok9"}, {"Name": "integrity", "Value": "sha256-wol/L08BZMXBVH1QM2xYeEOhBiYKyuw9rbG7I3jRyV4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "248131"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wol/L08BZMXBVH1QM2xYeEOhBiYKyuw9rbG7I3jRyV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wol/L08BZMXBVH1QM2xYeEOhBiYKyuw9rbG7I3jRyV4="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.e5t08xwnvm.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4627884"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"boDCTH4CcFrc811pESoOWTio/vFki03NclluP5oq7v0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e5t08xwnvm"}, {"Name": "integrity", "Value": "sha256-boDCTH4CcFrc811pESoOWTio/vFki03NclluP5oq7v0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4627884"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"boDCTH4CcFrc811pESoOWTio/vFki03NclluP5oq7v0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-boDCTH4CcFrc811pESoOWTio/vFki03NclluP5oq7v0="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.k2m5n6fmft.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k2m5n6fmft"}, {"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Data/scripts/data.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.eos21dnvjl.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "43200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ut0e5z1Fty2LB15FW1t6bbmN7MmbhphVxEzNL6eqdjE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eos21dnvjl"}, {"Name": "integrity", "Value": "sha256-Ut0e5z1Fty2LB15FW1t6bbmN7MmbhphVxEzNL6eqdjE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ut0e5z1Fty2LB15FW1t6bbmN7MmbhphVxEzNL6eqdjE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ut0e5z1Fty2LB15FW1t6bbmN7MmbhphVxEzNL6eqdjE="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.9j6p65atja.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j6p65<PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.4hzxl1uzz6.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4hzxl1uzz6"}, {"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.wdnlhme2ar.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wdnlhme2ar"}, {"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.ykoprjbkzz.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ykoprjbkzz"}, {"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.yo5q9ha208.js", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yo5q9ha208"}, {"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.bdnhf5zdvw.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bdnhf5zdvw"}, {"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.u8jmjqbpf2.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u8jmjqbpf2"}, {"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.fjcbrnxrtb.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fjcbrnxrtb"}, {"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.cg29fk2gn2.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg29fk2gn2"}, {"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.vteibwr79c.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vteibwr79c"}, {"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.x0gcv3gb9d.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0gcv3gb9d"}, {"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.lsufkzl165.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lsufkzl165"}, {"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.g9zntctz7a.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g9zntctz7a"}, {"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.j3z817ikhe.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j3z817ikhe"}, {"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.x6kv5h1gyc.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6kv5h1gyc"}, {"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.1zm61t9neq.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1zm61t9neq"}, {"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.m9eje2li2o.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m9eje2li2o"}, {"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.sm61wjcwzb.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sm61wjcwzb"}, {"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.mmw5aypzb0.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mmw5aypzb0"}, {"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.87p4l2g8lq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87p4l2g8lq"}, {"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.neeiqevn6f.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "neeiqevn6f"}, {"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.uqwhurys37.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqwhurys37"}, {"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.3yq1pja1s9.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3yq1pja1s9"}, {"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.7u9umw6dst.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7u9umw6dst"}, {"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.a20odbfi5s.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a20odbfi5s"}, {"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.aulwboaixq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aulwboaixq"}, {"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.3jpai5pxoj.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3jpai5pxoj"}, {"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q9EIsB+55S/uEt7FV53X+5JaxRJaqJhBqMr+FtHjZ9c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q9EIsB+55S/uEt7FV53X+5JaxRJaqJhBqMr+FtHjZ9c="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.l86jgekly0.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q9EIsB+55S/uEt7FV53X+5JaxRJaqJhBqMr+FtHjZ9c=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l86jgekly0"}, {"Name": "integrity", "Value": "sha256-q9EIsB+55S/uEt7FV53X+5JaxRJaqJhBqMr+FtHjZ9c="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.rb6ei14cmq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rb6ei14cmq"}, {"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.4yr0zuexvq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yr0zuexvq"}, {"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.viv3nqn9op.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "viv3nqn9op"}, {"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.xddooubi4o.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xddooubi4o"}, {"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.6kssvden7q.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lvIyBe/+rslK0CENk7ihdVQNOjmITIx+W355JgpMIiE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6kssvden7q"}, {"Name": "integrity", "Value": "sha256-lvIyBe/+rslK0CENk7ihdVQNOjmITIx+W355JgpMIiE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32254"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lvIyBe/+rslK0CENk7ihdVQNOjmITIx+W355JgpMIiE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lvIyBe/+rslK0CENk7ihdVQNOjmITIx+W355JgpMIiE="}]}, {"Route": "_content/Syncfusion.Blazor.ProgressBar/scripts/sf-progressbar.min.js", "AssetFile": "_content/Syncfusion.Blazor.ProgressBar/scripts/sf-progressbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7C/uGMqZCosPJ10STOtYFTCoDD1WtosH8+ylIjXysqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7C/uGMqZCosPJ10STOtYFTCoDD1WtosH8+ylIjXysqI="}]}, {"Route": "_content/Syncfusion.Blazor.ProgressBar/scripts/sf-progressbar.min.kipzg11se5.js", "AssetFile": "_content/Syncfusion.Blazor.ProgressBar/scripts/sf-progressbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7C/uGMqZCosPJ10STOtYFTCoDD1WtosH8+ylIjXysqI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kipzg11se5"}, {"Name": "integrity", "Value": "sha256-7C/uGMqZCosPJ10STOtYFTCoDD1WtosH8+ylIjXysqI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.ProgressBar/scripts/sf-progressbar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.jqs3n24tf7.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jqs3n24tf7"}, {"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.4h59cheqig.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4h59cheqig"}, {"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.y4pke33q7n.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y4pke33q7n"}, {"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.aphbptuiu1.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aphbptuiu1"}, {"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:09:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3527380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T09Fj+OkGFdW1TAKDWyyXVfZFct4Dn4YanUHO0HVgx8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T09Fj+OkGFdW1TAKDWyyXVfZFct4Dn4YanUHO0HVgx8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.jns5b1e7y1.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3527380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T09Fj+OkGFdW1TAKDWyyXVfZFct4Dn4YanUHO0HVgx8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jns5b1e7y1"}, {"Name": "integrity", "Value": "sha256-T09Fj+OkGFdW1TAKDWyyXVfZFct4Dn4YanUHO0HVgx8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3527375"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xKx5Y46fX5X1dGdILXD8gnIHwSIKZ2l4XkYLv+07Gb8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xKx5Y46fX5X1dGdILXD8gnIHwSIKZ2l4XkYLv+07Gb8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.op6tsss1mc.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3527375"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xKx5Y46fX5X1dGdILXD8gnIHwSIKZ2l4XkYLv+07Gb8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "op6tsss1mc"}, {"Name": "integrity", "Value": "sha256-xKx5Y46fX5X1dGdILXD8gnIHwSIKZ2l4XkYLv+07Gb8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3509282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"W/0IYOdA6XRcYcrqks/uPnL205zvKSS5lr1DsHZ6OQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W/0IYOdA6XRcYcrqks/uPnL205zvKSS5lr1DsHZ6OQQ="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.zusxdkfk00.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3509282"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"W/0IYOdA6XRcYcrqks/uPnL205zvKSS5lr1DsHZ6OQQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zusxdkfk00"}, {"Name": "integrity", "Value": "sha256-W/0IYOdA6XRcYcrqks/uPnL205zvKSS5lr1DsHZ6OQQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap.45amgewb4y.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3509277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2KSJAbwWHBlaASBRCOtnzqVOUDsPCrPALrrzQZedUds=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45amgewb4y"}, {"Name": "integrity", "Value": "sha256-2KSJAbwWHBlaASBRCOtnzqVOUDsPCrPALrrzQZedUds="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3509277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2KSJAbwWHBlaASBRCOtnzqVOUDsPCrPALrrzQZedUds=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2KSJAbwWHBlaASBRCOtnzqVOUDsPCrPALrrzQZedUds="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.8t4z2z9e7m.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3565415"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5D7xzapuy0LK2JkOwVX4c24g/SxG9dff5rBpHfNpN0I=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8t4z2z9e7m"}, {"Name": "integrity", "Value": "sha256-5D7xzapuy0LK2JkOwVX4c24g/SxG9dff5rBpHfNpN0I="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3565415"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5D7xzapuy0LK2JkOwVX4c24g/SxG9dff5rBpHfNpN0I=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5D7xzapuy0LK2JkOwVX4c24g/SxG9dff5rBpHfNpN0I="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3565410"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZgrP2CEbZ/j8C9TIGQyzW9HK6fC8EJ1Z8Em2A/o+sH8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZgrP2CEbZ/j8C9TIGQyzW9HK6fC8EJ1Z8Em2A/o+sH8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap4.ietvz3m0ew.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap4.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3565410"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZgrP2CEbZ/j8C9TIGQyzW9HK6fC8EJ1Z8Em2A/o+sH8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ietvz3m0ew"}, {"Name": "integrity", "Value": "sha256-ZgrP2CEbZ/j8C9TIGQyzW9HK6fC8EJ1Z8Em2A/o+sH8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap4.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3628488"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6stc6THjRdtTjXnu5iixXd02KPncLLma1xAHYDp9HXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6stc6THjRdtTjXnu5iixXd02KPncLLma1xAHYDp9HXA="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.e7qhhnswaw.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3628488"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"6stc6THjRdtTjXnu5iixXd02KPncLLma1xAHYDp9HXA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e7qhhnswaw"}, {"Name": "integrity", "Value": "sha256-6stc6THjRdtTjXnu5iixXd02KPncLLma1xAHYDp9HXA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.1cvc0rlvul.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3628483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oYVwySiuaXJrwRb02hmhnuqUPv74revEBnSw5vh4PBI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1cvc0rlvul"}, {"Name": "integrity", "Value": "sha256-oYVwySiuaXJrwRb02hmhnuqUPv74revEBnSw5vh4PBI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3628483"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oYVwySiuaXJrwRb02hmhnuqUPv74revEBnSw5vh4PBI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oYVwySiuaXJrwRb02hmhnuqUPv74revEBnSw5vh4PBI="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3597579"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JhO2LCepNwtcAAoq42T6zroKPuvQWxifjZ21QmL0Tg0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhO2LCepNwtcAAoq42T6zroKPuvQWxifjZ21QmL0Tg0="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.m8jzr7hdhx.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3597579"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JhO2LCepNwtcAAoq42T6zroKPuvQWxifjZ21QmL0Tg0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m8jzr7hdhx"}, {"Name": "integrity", "Value": "sha256-JhO2LCepNwtcAAoq42T6zroKPuvQWxifjZ21QmL0Tg0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3758241"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nmTgLgxO1Lrsv0WeqIwg5R46NxkZm3qyeWO6KWwYj/E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nmTgLgxO1Lrsv0WeqIwg5R46NxkZm3qyeWO6KWwYj/E="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.i09z7vkwon.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3758241"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"nmTgLgxO1Lrsv0WeqIwg5R46NxkZm3qyeWO6KWwYj/E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i09z7vkwon"}, {"Name": "integrity", "Value": "sha256-nmTgLgxO1Lrsv0WeqIwg5R46NxkZm3qyeWO6KWwYj/E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3758236"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kh9TH+M2LVDJ5zyBU4zcbKAA47ReFECe3WgzYNYGqy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kh9TH+M2LVDJ5zyBU4zcbKAA47ReFECe3WgzYNYGqy8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.uob1vkujm9.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3758236"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kh9TH+M2LVDJ5zyBU4zcbKAA47ReFECe3WgzYNYGqy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uob1vkujm9"}, {"Name": "integrity", "Value": "sha256-kh9TH+M2LVDJ5zyBU4zcbKAA47ReFECe3WgzYNYGqy8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.0l1msgz7hf.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3774664"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fILIH/WSgFn/58jeY62JGGFOKSHD6cYThoeGMuiAzZg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0l1msgz7hf"}, {"Name": "integrity", "Value": "sha256-fILIH/WSgFn/58jeY62JGGFOKSHD6cYThoeGMuiAzZg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3774664"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fILIH/WSgFn/58jeY62JGGFOKSHD6cYThoeGMuiAzZg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fILIH/WSgFn/58jeY62JGGFOKSHD6cYThoeGMuiAzZg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3774659"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"StUaqF8pMm5Fbe81yiXJZ/vVcfgF5ZfC6Ftw9uoldXg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-StUaqF8pMm5Fbe81yiXJZ/vVcfgF5ZfC6Ftw9uoldXg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.ie40n6j9vo.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3774659"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"StUaqF8pMm5Fbe81yiXJZ/vVcfgF5ZfC6Ftw9uoldXg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ie40n6j9vo"}, {"Name": "integrity", "Value": "sha256-StUaqF8pMm5Fbe81yiXJZ/vVcfgF5ZfC6Ftw9uoldXg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5.3.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3597574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"78prHrkoe4GUobsUoB8r2L4DE3k7LNDEFTKo/HRzrHA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-78prHrkoe4GUobsUoB8r2L4DE3k7LNDEFTKo/HRzrHA="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/bootstrap5.pirvo8m887.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3597574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"78prHrkoe4GUobsUoB8r2L4DE3k7LNDEFTKo/HRzrHA=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pirvo8m887"}, {"Name": "integrity", "Value": "sha256-78prHrkoe4GUobsUoB8r2L4DE3k7LNDEFTKo/HRzrHA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/bootstrap5.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material-dark.5ayyl3geor.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4156506"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DYMP2T0qkwg9yzDaKn2jBR9IJKXKVOh8Kut3G1371rk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ayyl3geor"}, {"Name": "integrity", "Value": "sha256-DYMP2T0qkwg9yzDaKn2jBR9IJKXKVOh8Kut3G1371rk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4156506"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DYMP2T0qkwg9yzDaKn2jBR9IJKXKVOh8Kut3G1371rk=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DYMP2T0qkwg9yzDaKn2jBR9IJKXKVOh8Kut3G1371rk="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4115493"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"G8dOcIYn7I4NuVZPaQ0o5CvevM++nI8D6Mwuc7NobW8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G8dOcIYn7I4NuVZPaQ0o5CvevM++nI8D6Mwuc7NobW8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/material.z23e43930v.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4115493"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"G8dOcIYn7I4NuVZPaQ0o5CvevM++nI8D6Mwuc7NobW8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z23e43930v"}, {"Name": "integrity", "Value": "sha256-G8dOcIYn7I4NuVZPaQ0o5CvevM++nI8D6Mwuc7NobW8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/customized/material.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.0jkeknsjgo.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3528689"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bC8Ub2qlCidLv60xt9p0NFDLIXI3f4wwSeaMgCvkzMU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0jkeknsjgo"}, {"Name": "integrity", "Value": "sha256-bC8Ub2qlCidLv60xt9p0NFDLIXI3f4wwSeaMgCvkzMU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3528689"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bC8Ub2qlCidLv60xt9p0NFDLIXI3f4wwSeaMgCvkzMU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bC8Ub2qlCidLv60xt9p0NFDLIXI3f4wwSeaMgCvkzMU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3490860"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2rBaSQpdxKL3Q8huy2bD6EgcW7dUNn7gaHINxW3tQ14=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2rBaSQpdxKL3Q8huy2bD6EgcW7dUNn7gaHINxW3tQ14="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/customized/tailwind.q3iuga6zql.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3490860"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2rBaSQpdxKL3Q8huy2bD6EgcW7dUNn7gaHINxW3tQ14=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q3iuga6zql"}, {"Name": "integrity", "Value": "sha256-2rBaSQpdxKL3Q8huy2bD6EgcW7dUNn7gaHINxW3tQ14="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/customized/tailwind.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3467889"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hD3uf+zUhdEKKLahuqqredb3+aWT2ehDG/vy6a1bfzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hD3uf+zUhdEKKLahuqqredb3+aWT2ehDG/vy6a1bfzQ="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.kc8du11xca.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3467889"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hD3uf+zUhdEKKLahuqqredb3+aWT2ehDG/vy6a1bfzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kc8du11xca"}, {"Name": "integrity", "Value": "sha256-hD3uf+zUhdEKKLahuqqredb3+aWT2ehDG/vy6a1bfzQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fabric-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark.8utdnjrjpc.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3467884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sBSQkBtrc2+78eA4eNYYsJDoTL4+m9JMf6nc4NNYY68=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8utdnjrjpc"}, {"Name": "integrity", "Value": "sha256-sBSQkBtrc2+78eA4eNYYsJDoTL4+m9JMf6nc4NNYY68="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fabric-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3467884"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sBSQkBtrc2+78eA4eNYYsJDoTL4+m9JMf6nc4NNYY68=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sBSQkBtrc2+78eA4eNYYsJDoTL4+m9JMf6nc4NNYY68="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3414186"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eZpMy0v5s/A3uENrbsMsvLgfCzJn5R8wFdFbaGiHFj0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eZpMy0v5s/A3uENrbsMsvLgfCzJn5R8wFdFbaGiHFj0="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric-lite.hufh8e3w9c.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3414186"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eZpMy0v5s/A3uENrbsMsvLgfCzJn5R8wFdFbaGiHFj0=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hufh8e3w9c"}, {"Name": "integrity", "Value": "sha256-eZpMy0v5s/A3uENrbsMsvLgfCzJn5R8wFdFbaGiHFj0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fabric-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3414181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bcJ4g5AdYHIW0Ja8A6+Ba79cZXilxGpDBg9MYLpIYRM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bcJ4g5AdYHIW0Ja8A6+Ba79cZXilxGpDBg9MYLpIYRM="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fabric.tgpytsq1xq.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fabric.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3414181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bcJ4g5AdYHIW0Ja8A6+Ba79cZXilxGpDBg9MYLpIYRM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgpytsq1xq"}, {"Name": "integrity", "Value": "sha256-bcJ4g5AdYHIW0Ja8A6+Ba79cZXilxGpDBg9MYLpIYRM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fabric.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3537387"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bSLd3Ik6vp4A+KIsqcUACD740sJDsqnSH1cOWdiWiUU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bSLd3Ik6vp4A+KIsqcUACD740sJDsqnSH1cOWdiWiUU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.hnqu8ew1hl.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3537387"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bSLd3Ik6vp4A+KIsqcUACD740sJDsqnSH1cOWdiWiUU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hnqu8ew1hl"}, {"Name": "integrity", "Value": "sha256-bSLd3Ik6vp4A+KIsqcUACD740sJDsqnSH1cOWdiWiUU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3537382"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dAx6eJhb7+Pg5Khcn+WwglP8CAb60KM8vm5F6zXc7DY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dAx6eJhb7+Pg5Khcn+WwglP8CAb60KM8vm5F6zXc7DY="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-dark.oxhzmead87.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3537382"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dAx6eJhb7+Pg5Khcn+WwglP8CAb60KM8vm5F6zXc7DY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oxhzmead87"}, {"Name": "integrity", "Value": "sha256-dAx6eJhb7+Pg5Khcn+WwglP8CAb60KM8vm5F6zXc7DY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3535420"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RGxVZcCTbQ+Cgbd8bNNLSdWM34F/SAm7+Nol469HxW4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RGxVZcCTbQ+Cgbd8bNNLSdWM34F/SAm7+Nol469HxW4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent-lite.opq2ww4fa1.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3535420"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RGxVZcCTbQ+Cgbd8bNNLSdWM34F/SAm7+Nol469HxW4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "opq2ww4fa1"}, {"Name": "integrity", "Value": "sha256-RGxVZcCTbQ+Cgbd8bNNLSdWM34F/SAm7+Nol469HxW4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent.cd176e6det.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3535415"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UJ/2jsr0HTX0qcthkfMXf2XScXEDJrZSG/pgtMmXqzY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cd176e6det"}, {"Name": "integrity", "Value": "sha256-UJ/2jsr0HTX0qcthkfMXf2XScXEDJrZSG/pgtMmXqzY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3535415"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UJ/2jsr0HTX0qcthkfMXf2XScXEDJrZSG/pgtMmXqzY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UJ/2jsr0HTX0qcthkfMXf2XScXEDJrZSG/pgtMmXqzY="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.7h4c1qo5mw.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4106688"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q6qdVUx+cuR6TJIPT5aMdLRkZAmdoyCxmCXUNr+hkqU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7h4c1qo5mw"}, {"Name": "integrity", "Value": "sha256-q6qdVUx+cuR6TJIPT5aMdLRkZAmdoyCxmCXUNr+hkqU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4106688"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q6qdVUx+cuR6TJIPT5aMdLRkZAmdoyCxmCXUNr+hkqU=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q6qdVUx+cuR6TJIPT5aMdLRkZAmdoyCxmCXUNr+hkqU="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4106683"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"U6LTm0siwaTR1mUV73TM2Pqxg6GbRl67LcaJtDjHDBI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U6LTm0siwaTR1mUV73TM2Pqxg6GbRl67LcaJtDjHDBI="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-dark.hjjjobtqr8.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4106683"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"U6LTm0siwaTR1mUV73TM2Pqxg6GbRl67LcaJtDjHDBI=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hjjjobtqr8"}, {"Name": "integrity", "Value": "sha256-U6LTm0siwaTR1mUV73TM2Pqxg6GbRl67LcaJtDjHDBI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.0tjnv3og4e.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4144239"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/FBbAFiLk6Su/ama0ymXSoFJYZqMVywSQyyKGGLUcb8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0tjnv3og4e"}, {"Name": "integrity", "Value": "sha256-/FBbAFiLk6Su/ama0ymXSoFJYZqMVywSQyyKGGLUcb8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4144239"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/FBbAFiLk6Su/ama0ymXSoFJYZqMVywSQyyKGGLUcb8=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/FBbAFiLk6Su/ama0ymXSoFJYZqMVywSQyyKGGLUcb8="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4144234"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/cILnCCn9syBBLJl0pId2uglc+Q1ySA1XHDWJ8SEFlw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/cILnCCn9syBBLJl0pId2uglc+Q1ySA1XHDWJ8SEFlw="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.v5fpkg9vey.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4144234"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/cILnCCn9syBBLJl0pId2uglc+Q1ySA1XHDWJ8SEFlw=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v5fpkg9vey"}, {"Name": "integrity", "Value": "sha256-/cILnCCn9syBBLJl0pId2uglc+Q1ySA1XHDWJ8SEFlw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2-highcontrast.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4146933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+qP+6cSgXyaSFDbXt0u1l1XzQkocDJNsyaPQhQhVo5Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+qP+6cSgXyaSFDbXt0u1l1XzQkocDJNsyaPQhQhVo5Y="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2-lite.q3wumyo5tk.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4146933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+qP+6cSgXyaSFDbXt0u1l1XzQkocDJNsyaPQhQhVo5Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q3wumyo5tk"}, {"Name": "integrity", "Value": "sha256-+qP+6cSgXyaSFDbXt0u1l1XzQkocDJNsyaPQhQhVo5Y="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4146928"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j/z1Q6TDnYt/Iep0XCoKLpUfTlvQ/4cwZiMLuFMPNHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j/z1Q6TDnYt/Iep0XCoKLpUfTlvQ/4cwZiMLuFMPNHM="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/fluent2.vwfwkgx8h4.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/fluent2.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4146928"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j/z1Q6TDnYt/Iep0XCoKLpUfTlvQ/4cwZiMLuFMPNHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwfwkgx8h4"}, {"Name": "integrity", "Value": "sha256-j/z1Q6TDnYt/Iep0XCoKLpUfTlvQ/4cwZiMLuFMPNHM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/fluent2.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3432233"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pcpe5vzuV7HYd08ajiQvqNDBEKf2URQsWPDUNyRPYEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pcpe5vzuV7HYd08ajiQvqNDBEKf2URQsWPDUNyRPYEM="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.vl3miqk5je.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3432233"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Pcpe5vzuV7HYd08ajiQvqNDBEKf2URQsWPDUNyRPYEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vl3miqk5je"}, {"Name": "integrity", "Value": "sha256-Pcpe5vzuV7HYd08ajiQvqNDBEKf2URQsWPDUNyRPYEM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/highcontrast-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast.7o014qk7te.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3432228"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WRouz/eeBXzQ2R5+7WDT1OjksjsWjjJ+0D+J/GolC6o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7o014qk7te"}, {"Name": "integrity", "Value": "sha256-WRouz/eeBXzQ2R5+7WDT1OjksjsWjjJ+0D+J/GolC6o="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/highcontrast.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/highcontrast.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/highcontrast.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3432228"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WRouz/eeBXzQ2R5+7WDT1OjksjsWjjJ+0D+J/GolC6o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WRouz/eeBXzQ2R5+7WDT1OjksjsWjjJ+0D+J/GolC6o="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4156579"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OKgZYonfpqW8WpHrIb3DrMJ92PfiDMmO0p4HJmUsb8w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OKgZYonfpqW8WpHrIb3DrMJ92PfiDMmO0p4HJmUsb8w="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark-lite.w61rtvg8nq.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4156579"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OKgZYonfpqW8WpHrIb3DrMJ92PfiDMmO0p4HJmUsb8w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "w61rtvg8nq"}, {"Name": "integrity", "Value": "sha256-OKgZYonfpqW8WpHrIb3DrMJ92PfiDMmO0p4HJmUsb8w="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4156574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"68BsIzgWn0AR++oWxq3PIUMPwClN8LR6YBWit9DmFGg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-68BsIzgWn0AR++oWxq3PIUMPwClN8LR6YBWit9DmFGg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-dark.fj3wfukkw7.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4156574"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"68BsIzgWn0AR++oWxq3PIUMPwClN8LR6YBWit9DmFGg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fj3wfukkw7"}, {"Name": "integrity", "Value": "sha256-68BsIzgWn0AR++oWxq3PIUMPwClN8LR6YBWit9DmFGg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4115566"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"R3uVvsU05Q7pYF9JZRgtgz7xMS4Jwf/wkLczZfK+b5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R3uVvsU05Q7pYF9JZRgtgz7xMS4Jwf/wkLczZfK+b5M="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material-lite.x2tanjrqae.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4115566"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"R3uVvsU05Q7pYF9JZRgtgz7xMS4Jwf/wkLczZfK+b5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x2tanjrqae"}, {"Name": "integrity", "Value": "sha256-R3uVvsU05Q7pYF9JZRgtgz7xMS4Jwf/wkLczZfK+b5M="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4115561"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sxe/ZNgVviWkPGcJW9V1qGXTb/ecRh8g2ySbxTgF8gE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxe/ZNgVviWkPGcJW9V1qGXTb/ecRh8g2ySbxTgF8gE="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material.hbffdg4h00.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4115561"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sxe/ZNgVviWkPGcJW9V1qGXTb/ecRh8g2ySbxTgF8gE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hbffdg4h00"}, {"Name": "integrity", "Value": "sha256-sxe/ZNgVviWkPGcJW9V1qGXTb/ecRh8g2ySbxTgF8gE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4236117"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sUQX7hRux10lL0GSDLG+eCeQHdcYMW0zdgovlAw5Vvg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sUQX7hRux10lL0GSDLG+eCeQHdcYMW0zdgovlAw5Vvg="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.hsjy0rqn0o.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4236117"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sUQX7hRux10lL0GSDLG+eCeQHdcYMW0zdgovlAw5Vvg=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hsjy0rqn0o"}, {"Name": "integrity", "Value": "sha256-sUQX7hRux10lL0GSDLG+eCeQHdcYMW0zdgovlAw5Vvg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material3-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark.a006gscyiw.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4236112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ioXTNRHEZZ4Ig9Ie2CDRY4AC34DHD8TDsEzregFEkPE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a006gscyiw"}, {"Name": "integrity", "Value": "sha256-ioXTNRHEZZ4Ig9Ie2CDRY4AC34DHD8TDsEzregFEkPE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material3-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4236112"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ioXTNRHEZZ4Ig9Ie2CDRY4AC34DHD8TDsEzregFEkPE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ioXTNRHEZZ4Ig9Ie2CDRY4AC34DHD8TDsEzregFEkPE="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4236996"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2IUepJnRaxKBduaHT+sKohmQwFgzLdy5jW52aueJh0E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2IUepJnRaxKBduaHT+sKohmQwFgzLdy5jW52aueJh0E="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3-lite.owdzdbd1of.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4236996"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2IUepJnRaxKBduaHT+sKohmQwFgzLdy5jW52aueJh0E=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "owdzdbd1of"}, {"Name": "integrity", "Value": "sha256-2IUepJnRaxKBduaHT+sKohmQwFgzLdy5jW52aueJh0E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material3-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4236991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"suRn/t8Uagf0a+PesxdZ2SsAo7VdA1pT225vcnMrs0o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-suRn/t8Uagf0a+PesxdZ2SsAo7VdA1pT225vcnMrs0o="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/material3.uk6cj394sj.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/material3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4236991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"suRn/t8Uagf0a+PesxdZ2SsAo7VdA1pT225vcnMrs0o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uk6cj394sj"}, {"Name": "integrity", "Value": "sha256-suRn/t8Uagf0a+PesxdZ2SsAo7VdA1pT225vcnMrs0o="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/material3.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.1dcpzvz024.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3528804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K+YRbvLNlpazDc1qPKQ5AXD9oq61RgKhfFoclZEmF8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dcpzvz024"}, {"Name": "integrity", "Value": "sha256-K+YRbvLNlpazDc1qPKQ5AXD9oq61RgKhfFoclZEmF8k="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3528804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K+YRbvLNlpazDc1qPKQ5AXD9oq61RgKhfFoclZEmF8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K+YRbvLNlpazDc1qPKQ5AXD9oq61RgKhfFoclZEmF8k="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark.bin3aw4t1g.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3528799"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PRz+eIq+cC6+3+ThCFickMwXAdQvfSWL3fYvko1nSSE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bin3aw4t1g"}, {"Name": "integrity", "Value": "sha256-PRz+eIq+cC6+3+ThCFickMwXAdQvfSWL3fYvko1nSSE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3528799"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PRz+eIq+cC6+3+ThCFickMwXAdQvfSWL3fYvko1nSSE=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PRz+eIq+cC6+3+ThCFickMwXAdQvfSWL3fYvko1nSSE="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-lite.3okzcu2cuw.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3490975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YzfTS1dz3Sgb3cDxtpkmenEZIduyr/xxFVnhlBBrWN4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3okzcu2cuw"}, {"Name": "integrity", "Value": "sha256-YzfTS1dz3Sgb3cDxtpkmenEZIduyr/xxFVnhlBBrWN4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3490975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YzfTS1dz3Sgb3cDxtpkmenEZIduyr/xxFVnhlBBrWN4=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YzfTS1dz3Sgb3cDxtpkmenEZIduyr/xxFVnhlBBrWN4="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind.1tkq3fsyf7.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3490970"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1VrcuV7ySS1OLpDS6lqaF7/t7MGVeNw3Ckm924/XKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1tkq3fsyf7"}, {"Name": "integrity", "Value": "sha256-K1VrcuV7ySS1OLpDS6lqaF7/t7MGVeNw3Ckm924/XKc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3490970"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K1VrcuV7ySS1OLpDS6lqaF7/t7MGVeNw3Ckm924/XKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K1VrcuV7ySS1OLpDS6lqaF7/t7MGVeNw3Ckm924/XKc="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-dark-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4166503"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hEcRRY5JCC66/0TrFQqwsXPR/Dpia36WphGggLsqK3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hEcRRY5JCC66/0TrFQqwsXPR/Dpia36WphGggLsqK3o="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-dark-lite.ssfauzo2o1.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-dark-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4166503"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hEcRRY5JCC66/0TrFQqwsXPR/Dpia36WphGggLsqK3o=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ssfauzo2o1"}, {"Name": "integrity", "Value": "sha256-hEcRRY5JCC66/0TrFQqwsXPR/Dpia36WphGggLsqK3o="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind3-dark-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-dark.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4166498"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3Vxp3lmzJefvejdkxIkNy3ZUCXx2M6YE75Cx2B4zP0w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3Vxp3lmzJefvejdkxIkNy3ZUCXx2M6YE75Cx2B4zP0w="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-dark.zkbyofk1o1.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-dark.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4166498"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3Vxp3lmzJefvejdkxIkNy3ZUCXx2M6YE75Cx2B4zP0w=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zkbyofk1o1"}, {"Name": "integrity", "Value": "sha256-3Vxp3lmzJefvejdkxIkNy3ZUCXx2M6YE75Cx2B4zP0w="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind3-dark.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-lite.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4184277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kc4Ehz8CWb+ORSzkrpzcIgPJ7SabFgB+IE7t6vCbBaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kc4Ehz8CWb+ORSzkrpzcIgPJ7SabFgB+IE7t6vCbBaY="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3-lite.n296a152k8.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3-lite.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4184277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kc4Ehz8CWb+ORSzkrpzcIgPJ7SabFgB+IE7t6vCbBaY=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n296a152k8"}, {"Name": "integrity", "Value": "sha256-kc4Ehz8CWb+ORSzkrpzcIgPJ7SabFgB+IE7t6vCbBaY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind3-lite.css"}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4184272"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RfPbYMQ7LaAYjQgF4Z0pivl8lStGHjmNQ5ZAkpIPv9Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RfPbYMQ7LaAYjQgF4Z0pivl8lStGHjmNQ5ZAkpIPv9Q="}]}, {"Route": "_content/Syncfusion.Blazor.Themes/tailwind3.d2rpuoyhb7.css", "AssetFile": "_content/Syncfusion.Blazor.Themes/tailwind3.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4184272"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RfPbYMQ7LaAYjQgF4Z0pivl8lStGHjmNQ5ZAkpIPv9Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 21 Jul 2025 10:07:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2rpuoyhb7"}, {"Name": "integrity", "Value": "sha256-RfPbYMQ7LaAYjQgF4Z0pivl8lStGHjmNQ5ZAkpIPv9Q="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Themes/tailwind3.css"}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Jul 2025 13:50:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "images/logo_wafalld.dyfrypy1zt.png", "AssetFile": "images/logo_wafalld.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8466"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"e7AVKzpcaFKcL9gVPTd/zwALieqdS2HhxI/K79jS934=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 08:50:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dyfrypy1zt"}, {"Name": "integrity", "Value": "sha256-e7AVKzpcaFKcL9gVPTd/zwALieqdS2HhxI/K79jS934="}, {"Name": "label", "Value": "images/logo_wafalld.png"}]}, {"Route": "images/logo_wafalld.png", "AssetFile": "images/logo_wafalld.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8466"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"e7AVKzpcaFKcL9gVPTd/zwALieqdS2HhxI/K79jS934=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 08:50:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e7AVKzpcaFKcL9gVPTd/zwALieqdS2HhxI/K79jS934="}]}, {"Route": "images/new_logo.1borrbkkny.png", "AssetFile": "images/new_logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "35554"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MwWa4m9dbd//mH3smFUjbhxmGNmQDoafXZcshGTKKls=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:53:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1borrbkkny"}, {"Name": "integrity", "Value": "sha256-MwWa4m9dbd//mH3smFUjbhxmGNmQDoafXZcshGTKKls="}, {"Name": "label", "Value": "images/new_logo.png"}]}, {"Route": "images/new_logo.png", "AssetFile": "images/new_logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35554"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MwWa4m9dbd//mH3smFUjbhxmGNmQDoafXZcshGTKKls=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:53:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwWa4m9dbd//mH3smFUjbhxmGNmQDoafXZcshGTKKls="}]}, {"Route": "shared-page-styles.9h9sh7lhfe.css", "AssetFile": "shared-page-styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AyT4ODFuRtzQE4/5NUpHYMrCGNNsIa7I+CaNdt/wj/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 11:20:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9h9sh7lhfe"}, {"Name": "integrity", "Value": "sha256-AyT4ODFuRtzQE4/5NUpHYMrCGNNsIa7I+CaNdt/wj/o="}, {"Name": "label", "Value": "shared-page-styles.css"}]}, {"Route": "shared-page-styles.css", "AssetFile": "shared-page-styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1220"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AyT4ODFuRtzQE4/5NUpHYMrCGNNsIa7I+CaNdt/wj/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 11:20:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AyT4ODFuRtzQE4/5NUpHYMrCGNNsIa7I+CaNdt/wj/o="}]}]}