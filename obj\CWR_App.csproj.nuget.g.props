﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.themes\30.1.41\buildTransitive\Syncfusion.Blazor.Themes.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.themes\30.1.41\buildTransitive\Syncfusion.Blazor.Themes.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.core\30.1.41\buildTransitive\Syncfusion.Blazor.Core.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.core\30.1.41\buildTransitive\Syncfusion.Blazor.Core.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.spinner\30.1.41\buildTransitive\Syncfusion.Blazor.Spinner.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.spinner\30.1.41\buildTransitive\Syncfusion.Blazor.Spinner.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.buttons\30.1.41\buildTransitive\Syncfusion.Blazor.Buttons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.buttons\30.1.41\buildTransitive\Syncfusion.Blazor.Buttons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.popups\30.1.41\buildTransitive\Syncfusion.Blazor.Popups.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.popups\30.1.41\buildTransitive\Syncfusion.Blazor.Popups.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\30.1.41\buildTransitive\Syncfusion.Blazor.SplitButtons.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.splitbuttons\30.1.41\buildTransitive\Syncfusion.Blazor.SplitButtons.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.data\30.1.41\buildTransitive\Syncfusion.Blazor.Data.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.data\30.1.41\buildTransitive\Syncfusion.Blazor.Data.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.progressbar\30.1.41\buildTransitive\Syncfusion.Blazor.ProgressBar.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.progressbar\30.1.41\buildTransitive\Syncfusion.Blazor.ProgressBar.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.notifications\30.1.41\buildTransitive\Syncfusion.Blazor.Notifications.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.notifications\30.1.41\buildTransitive\Syncfusion.Blazor.Notifications.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.lists\30.1.41\buildTransitive\Syncfusion.Blazor.Lists.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.lists\30.1.41\buildTransitive\Syncfusion.Blazor.Lists.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.inputs\30.1.41\buildTransitive\Syncfusion.Blazor.Inputs.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.inputs\30.1.41\buildTransitive\Syncfusion.Blazor.Inputs.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.dropdowns\30.1.41\buildTransitive\Syncfusion.Blazor.DropDowns.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.dropdowns\30.1.41\buildTransitive\Syncfusion.Blazor.DropDowns.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.navigations\30.1.41\buildTransitive\Syncfusion.Blazor.Navigations.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.navigations\30.1.41\buildTransitive\Syncfusion.Blazor.Navigations.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.calendars\30.1.41\buildTransitive\Syncfusion.Blazor.Calendars.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.calendars\30.1.41\buildTransitive\Syncfusion.Blazor.Calendars.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.grid\30.1.41\buildTransitive\Syncfusion.Blazor.Grid.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.grid\30.1.41\buildTransitive\Syncfusion.Blazor.Grid.props')" />
    <Import Project="$(NuGetPackageRoot)syncfusion.blazor.charts\30.1.41\buildTransitive\Syncfusion.Blazor.Charts.props" Condition="Exists('$(NuGetPackageRoot)syncfusion.blazor.charts\30.1.41\buildTransitive\Syncfusion.Blazor.Charts.props')" />
  </ImportGroup>
</Project>