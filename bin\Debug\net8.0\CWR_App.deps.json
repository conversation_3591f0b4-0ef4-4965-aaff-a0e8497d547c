{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CWR_App/1.0.0": {"dependencies": {"Syncfusion.Blazor.Charts": "30.1.41", "Syncfusion.Blazor.Grid": "30.1.41", "Syncfusion.Blazor.ProgressBar": "30.1.41", "Syncfusion.Blazor.Themes": "30.1.41"}, "runtime": {"CWR_App.dll": {}}}, "Microsoft.AspNetCore.Authorization/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.15", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.15", "Microsoft.AspNetCore.Components.Analyzers": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.15", "Microsoft.AspNetCore.Components.Forms": "8.0.15", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.15", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Metadata/8.0.15": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.JSInterop/8.0.15": {"runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Syncfusion.Blazor.Buttons/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Calendars/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Inputs": "30.1.41", "Syncfusion.Blazor.Lists": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Calendars.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Charts/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41", "Syncfusion.Blazor.DataVizCommon": "30.1.41", "Syncfusion.ExcelExport.Net.Core": "30.1.41", "Syncfusion.PdfExport.Net.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Charts.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Core/30.1.41": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.15", "Syncfusion.Licensing": "30.1.41", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Data/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.DataVizCommon/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DataVizCommon.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.DropDowns/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41", "Syncfusion.Blazor.Inputs": "30.1.41", "Syncfusion.Blazor.Notifications": "30.1.41", "Syncfusion.Blazor.Spinner": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Grid/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Calendars": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41", "Syncfusion.Blazor.DropDowns": "30.1.41", "Syncfusion.Blazor.Inputs": "30.1.41", "Syncfusion.Blazor.Navigations": "30.1.41", "Syncfusion.Blazor.Popups": "30.1.41", "Syncfusion.Blazor.Spinner": "30.1.41", "Syncfusion.ExcelExport.Net.Core": "30.1.41", "Syncfusion.PdfExport.Net.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Grids.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Inputs/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41", "Syncfusion.Blazor.Popups": "30.1.41", "Syncfusion.Blazor.Spinner": "30.1.41", "Syncfusion.Blazor.SplitButtons": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Lists/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Navigations/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41", "Syncfusion.Blazor.DropDowns": "30.1.41", "Syncfusion.Blazor.Inputs": "30.1.41", "Syncfusion.Blazor.Lists": "30.1.41", "Syncfusion.Blazor.Popups": "30.1.41", "Syncfusion.Blazor.Spinner": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Notifications/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Popups/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.ProgressBar/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Data": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.ProgressBar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Spinner/30.1.41": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.SplitButtons/30.1.41": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.41", "Syncfusion.Blazor.Core": "30.1.41", "Syncfusion.Blazor.Popups": "30.1.41", "Syncfusion.Blazor.Spinner": "30.1.41"}, "runtime": {"lib/net8.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Blazor.Themes/30.1.41": {"runtime": {"lib/net8.0/Syncfusion.Blazor.Themes.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.ExcelExport.Net.Core/30.1.41": {"runtime": {"lib/net8.0/Syncfusion.ExcelExport.Net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.Licensing/30.1.41": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Syncfusion.PdfExport.Net.Core/30.1.41": {"runtime": {"lib/net8.0/Syncfusion.PdfExport.Net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Text.Json/8.0.5": {}}}, "libraries": {"CWR_App/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-AIpppyCOYv0hJjmYqytMSVohAV8THV6sTk7VoM6rHo1Jq0D1fOfkRi9lnmEz20nfKt50wX2OlciEQop3JlMC6A==", "path": "microsoft.aspnetcore.authorization/8.0.15", "hashPath": "microsoft.aspnetcore.authorization.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eQIgPU1oTkqR8hWvDjyZFGBBtgPY4bYl4a/WN7g0txmoww+fbE1nZ4D+5MeSbZu1kfHWX/Ruog/pMF9zUX6D7g==", "path": "microsoft.aspnetcore.components/8.0.15", "hashPath": "microsoft.aspnetcore.components.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-Uqc/wecFqwfpC9bUK9DH3W3nrriRM6ZBL8OozV6GHNNgrqvzdnqsPKo3NcmjhokvMo2/XHTrreRC2yA2aTLS/A==", "path": "microsoft.aspnetcore.components.analyzers/8.0.15", "hashPath": "microsoft.aspnetcore.components.analyzers.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-579O5c3u0FXnOAjk6JNzcy+/+qazMbedAeOy1vT1B6CBYovG8iGJhyUukqCbHcNQq3/lqxBTyB9rFzlH5zOttA==", "path": "microsoft.aspnetcore.components.forms/8.0.15", "hashPath": "microsoft.aspnetcore.components.forms.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-ttRxlR+yM0sNi+Ecw0ze26l2IcBIF2L+YNQGs4v159v+Ge51yn8bwehnlzs9MY5y3Xamvek4l71uZfk1oouYSA==", "path": "microsoft.aspnetcore.components.web/8.0.15", "hashPath": "microsoft.aspnetcore.components.web.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eKlqQasjNZD0B3thB+nX7kkpG/RiZlXObmjX+JurWycnssGp1q9iZjKn2SqKA45Pouw+529aQUYYP2/QCE5xIQ==", "path": "microsoft.aspnetcore.metadata/8.0.15", "hashPath": "microsoft.aspnetcore.metadata.8.0.15.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "path": "microsoft.extensions.logging.abstractions/8.0.3", "hashPath": "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.JSInterop/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-JQjYabj1o3M5rUp/GM8uVlLpgc9tgxZea+89IkvGl8AcY7Tfwn0Q5qltVa9rfm3mzCsr2ecVCKzWMSMYiXS0Kg==", "path": "microsoft.jsinterop/8.0.15", "hashPath": "microsoft.jsinterop.8.0.15.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-n7HHD/YdWWzdUtwtKgdtjHgH1+evImuhSuxTbLTmTNAiU76IDVfCqW3q9OnAwcSBr2/C0HfFPn/lCJ6sXMp+hQ==", "path": "syncfusion.blazor.buttons/30.1.41", "hashPath": "syncfusion.blazor.buttons.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Calendars/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-8zHm6GUqCnL2yO8gLg4DuuR/QOw40CzfEKTR5IIdCVVNGKsQJvbpHV14l0kHcMCLExfbyT+s7Q2vLKDHAqoUxw==", "path": "syncfusion.blazor.calendars/30.1.41", "hashPath": "syncfusion.blazor.calendars.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Charts/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-lMCQQTSC3f1Qn97mhae/eXqGpXDRnVmEAz9+yZlFmLTGSU2JVKEgCEoKC93D+F9AYirVFSl/IDtS6tAYKwXDdw==", "path": "syncfusion.blazor.charts/30.1.41", "hashPath": "syncfusion.blazor.charts.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Core/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-3kFNt8GQpkkJd/DqY5aESQzZ+xumn62OlpwzzhQQtEzHNZmB0GmDhTPZfpCKnLakl1LMVdu6LhZ3PuETjV6VsQ==", "path": "syncfusion.blazor.core/30.1.41", "hashPath": "syncfusion.blazor.core.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Data/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-G45YfRgbuiVg3ExOFQDpi8ZEF+a69yA8UvFjNN5hdTnyXuMQCNJMOsmzIs/NTEI8qJ6SWwDZLD5RqyYNWiC2BA==", "path": "syncfusion.blazor.data/30.1.41", "hashPath": "syncfusion.blazor.data.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.DataVizCommon/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-hJpqHhvJtxy5hLp9rafCiETJUjqIAhr4q0Xb3UwZkgSXL4/w6tm12HvmlUSnuz5o9GrBufZBsrjB6w+rNWqeeQ==", "path": "syncfusion.blazor.datavizcommon/30.1.41", "hashPath": "syncfusion.blazor.datavizcommon.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-yznvpwdA0TRCxG0BMj99XfsZUOcqkcWoPn32+yB4hcAcmLK/dKrPM28zpvINNYwHBOkFgLfSJm0F+tMUIwdKcA==", "path": "syncfusion.blazor.dropdowns/30.1.41", "hashPath": "syncfusion.blazor.dropdowns.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Grid/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-NMZuPLFGrqD4bpBvU4WCR+PLXkQTgsYpYcRCbOcrN0F0+9JMbW5oHobo9/IQUsUO8yxuqoNixsJQZKvA4y3ubg==", "path": "syncfusion.blazor.grid/30.1.41", "hashPath": "syncfusion.blazor.grid.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-5FxpSKp/9TMF/kXkkU1dFutjO+F8gxHK+H++w7B7rcwdsUoXTQWPLqctynvd3Jje3I02NdmnFNyrge/1vUxF2g==", "path": "syncfusion.blazor.inputs/30.1.41", "hashPath": "syncfusion.blazor.inputs.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Lists/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-o79/GPPJZG9yh79ak0/p6l6hdSo/2TbRw6VFnBtujDFzWiX7ojOGalVinNkWy7ipF26dZunu7mM4zUlgqqhgKw==", "path": "syncfusion.blazor.lists/30.1.41", "hashPath": "syncfusion.blazor.lists.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-Vd6PuQFWBVY1jE/N3dtz5ihtXUYskA/qjRii65qhA/3RSCUnBM4I2UgAyCBEAA2dBq/JeSaE5Q1c8PJ0Tvnnow==", "path": "syncfusion.blazor.navigations/30.1.41", "hashPath": "syncfusion.blazor.navigations.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-NGfebsE/seWC+yrxDvjLpGKvSEx73cE7ChiYc0ngijjgjyJpESOnHNv6b5INiNHOFrL6q616mPBBTQ1fQgMfDA==", "path": "syncfusion.blazor.notifications/30.1.41", "hashPath": "syncfusion.blazor.notifications.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Popups/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-OSo+JgsSrEYDQt65WWT1Jo9WmEhjr1seX8sctbB1aevd5R1Z1Td0P3tld8santV+76RZhjjM1AIXyGVEZMtziQ==", "path": "syncfusion.blazor.popups/30.1.41", "hashPath": "syncfusion.blazor.popups.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.ProgressBar/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-uO+Fk6SBmvXEJBDErmBcLCdVioav1f2AWYnduBh3R2nujL9d6toS019IXMv48338LaeHdayrcbREiopj8yT9GA==", "path": "syncfusion.blazor.progressbar/30.1.41", "hashPath": "syncfusion.blazor.progressbar.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-9b/hneDnTod6Kxf5TNcBpmAyCzqV4W1bixjpNPxvMYQR9NWYXlLODmxQE3mL8rirXXHgo6wWYhl9j9kfAJ9uSQ==", "path": "syncfusion.blazor.spinner/30.1.41", "hashPath": "syncfusion.blazor.spinner.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-UkeMVkNCkkV2RuDy8y6tdAnWuKKhaJLeN4IBPOscCT880qAd/iCcMFNtlIOmeA1i+Cwy6dANRheqI/2diK5fAQ==", "path": "syncfusion.blazor.splitbuttons/30.1.41", "hashPath": "syncfusion.blazor.splitbuttons.30.1.41.nupkg.sha512"}, "Syncfusion.Blazor.Themes/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-P59SfG6S4i6SnfGWnv8qjq4Idqz6n0HVSs4e4L/5KXeECOwQvafkGPBzk2DH9VMjoXNryCfI+mlgAkcSztZK1g==", "path": "syncfusion.blazor.themes/30.1.41", "hashPath": "syncfusion.blazor.themes.30.1.41.nupkg.sha512"}, "Syncfusion.ExcelExport.Net.Core/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-PJ3aP9hrpfLtgeEuOu24BP7aHJx4c7eozUS2bQIoJQ8BUliAqY0abYrSHfKUoxPggT3+jLCBqbos9BhY0E+ylg==", "path": "syncfusion.excelexport.net.core/30.1.41", "hashPath": "syncfusion.excelexport.net.core.30.1.41.nupkg.sha512"}, "Syncfusion.Licensing/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-BJQQBEXtO+mYDdzWIVt0dfooV9KOWtCCYl3SV0sR/QPZSRRITGztKbyaXBzCvLumi21kVznvVJp2ql2cjDCFYw==", "path": "syncfusion.licensing/30.1.41", "hashPath": "syncfusion.licensing.30.1.41.nupkg.sha512"}, "Syncfusion.PdfExport.Net.Core/30.1.41": {"type": "package", "serviceable": true, "sha512": "sha512-xeiFAeo9KNF1nEeOb2sMoc9F+CEeYs7k3JqdJWTLsocOg12CsJumM+9ja9/Q0rphsYleoaOMkoT0ivAeQa8UIA==", "path": "syncfusion.pdfexport.net.core/30.1.41", "hashPath": "syncfusion.pdfexport.net.core.30.1.41.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}}}