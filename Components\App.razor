﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="shared-page-styles.css" />
    <link rel="stylesheet" href="CWR_App.styles.css" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet />
</head>

<body>
    <Routes />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>

    <script>
        let resizeTimeout;
        let sidebarComponent = null;
        let currentMode = null;

        window.addResizeListener = (dotNetHelper) => {
            sidebarComponent = dotNetHelper;

            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    if (sidebarComponent) {
                        // Show transition indicator
                        showModeTransition();

                        sidebarComponent.invokeMethodAsync('OnWindowResize').then(() => {
                            // Hide transition indicator after mode switch
                            setTimeout(hideModeTransition, 300);
                        });
                    }
                }, 250);
            });
        };

        // Enhanced mode transition feedback
        function showModeTransition() {
            const overlay = document.createElement('div');
            overlay.id = 'mode-transition-overlay';
            overlay.className = 'mode-transition-overlay active';
            overlay.innerHTML = '<div class="transition-spinner"></div>';
            document.body.appendChild(overlay);
        }

        function hideModeTransition() {
            const overlay = document.getElementById('mode-transition-overlay');
            if (overlay) {
                overlay.classList.remove('active');
                setTimeout(() => overlay.remove(), 300);
            }
        }

        // Enhanced touch handling
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // Performance optimization: debounce rapid mode changes
        let modeChangeTimeout;
        window.addEventListener('modeChanged', function(event) {
            clearTimeout(modeChangeTimeout);
            modeChangeTimeout = setTimeout(() => {
                currentMode = event.detail.isMobile ? 'mobile' : 'desktop';
                console.log('App: Stabilized in', currentMode, 'mode');

                // Apply global mode-specific optimizations
                document.body.classList.toggle('mobile-mode', event.detail.isMobile);
                document.body.classList.toggle('desktop-mode', !event.detail.isMobile);
            }, 100);
        });
    </script>
</body>

</html>
