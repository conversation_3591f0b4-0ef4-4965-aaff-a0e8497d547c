{"ContentRoots": ["C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\wwwroot\\", "C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.charts\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.progressbar\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.41\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.41\\staticwebassets\\"], "Root": {"Children": {"app.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "app.css"}, "Patterns": null}, "CWR_App.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "CWR_App.styles.css"}, "Patterns": null}, "favicon.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "favicon.png"}, "Patterns": null}, "shared-page-styles.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "shared-page-styles.css"}, "Patterns": null}, "bootstrap": {"Children": {"bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "bootstrap/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "bootstrap/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "images": {"Children": {"logo_wafalld.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "images/logo_wafalld.png"}, "Patterns": null}, "new_logo.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "images/new_logo.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "_content": {"Children": {"Syncfusion.Blazor.Themes": {"Children": {"bootstrap-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap-dark-lite.css"}, "Patterns": null}, "bootstrap-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap-dark.css"}, "Patterns": null}, "bootstrap-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap-lite.css"}, "Patterns": null}, "bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap.css"}, "Patterns": null}, "bootstrap4-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap4-lite.css"}, "Patterns": null}, "bootstrap4.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap4.css"}, "Patterns": null}, "bootstrap5-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5-dark-lite.css"}, "Patterns": null}, "bootstrap5-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5-dark.css"}, "Patterns": null}, "bootstrap5-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5-lite.css"}, "Patterns": null}, "bootstrap5.3-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5.3-dark-lite.css"}, "Patterns": null}, "bootstrap5.3-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5.3-dark.css"}, "Patterns": null}, "bootstrap5.3-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5.3-lite.css"}, "Patterns": null}, "bootstrap5.3.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5.3.css"}, "Patterns": null}, "bootstrap5.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "bootstrap5.css"}, "Patterns": null}, "fabric-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fabric-dark-lite.css"}, "Patterns": null}, "fabric-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fabric-dark.css"}, "Patterns": null}, "fabric-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fabric-lite.css"}, "Patterns": null}, "fabric.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fabric.css"}, "Patterns": null}, "fluent-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent-dark-lite.css"}, "Patterns": null}, "fluent-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent-dark.css"}, "Patterns": null}, "fluent-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent-lite.css"}, "Patterns": null}, "fluent.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent.css"}, "Patterns": null}, "fluent2-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2-dark-lite.css"}, "Patterns": null}, "fluent2-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2-dark.css"}, "Patterns": null}, "fluent2-highcontrast-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2-highcontrast-lite.css"}, "Patterns": null}, "fluent2-highcontrast.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2-highcontrast.css"}, "Patterns": null}, "fluent2-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2-lite.css"}, "Patterns": null}, "fluent2.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "fluent2.css"}, "Patterns": null}, "highcontrast-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "highcontrast-lite.css"}, "Patterns": null}, "highcontrast.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "highcontrast.css"}, "Patterns": null}, "material-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material-dark-lite.css"}, "Patterns": null}, "material-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material-dark.css"}, "Patterns": null}, "material-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material-lite.css"}, "Patterns": null}, "material.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material.css"}, "Patterns": null}, "material3-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material3-dark-lite.css"}, "Patterns": null}, "material3-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material3-dark.css"}, "Patterns": null}, "material3-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material3-lite.css"}, "Patterns": null}, "material3.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "material3.css"}, "Patterns": null}, "tailwind-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind-dark-lite.css"}, "Patterns": null}, "tailwind-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind-dark.css"}, "Patterns": null}, "tailwind-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind-lite.css"}, "Patterns": null}, "tailwind.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind.css"}, "Patterns": null}, "tailwind3-dark-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind3-dark-lite.css"}, "Patterns": null}, "tailwind3-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind3-dark.css"}, "Patterns": null}, "tailwind3-lite.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind3-lite.css"}, "Patterns": null}, "tailwind3.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "tailwind3.css"}, "Patterns": null}, "customized": {"Children": {"material-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "customized/material-dark.css"}, "Patterns": null}, "material.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "customized/material.css"}, "Patterns": null}, "tailwind-dark.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "customized/tailwind-dark.css"}, "Patterns": null}, "tailwind.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "customized/tailwind.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Buttons": {"Children": {"scripts": {"Children": {"sf-floating-action-button.min.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "scripts/sf-floating-action-button.min.js"}, "Patterns": null}, "sf-speeddial.min.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "scripts/sf-speeddial.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Calendars": {"Children": {"scripts": {"Children": {"sf-calendar.min.js": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "scripts/sf-calendar.min.js"}, "Patterns": null}, "sf-datepicker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "scripts/sf-datepicker.min.js"}, "Patterns": null}, "sf-daterangepicker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "scripts/sf-daterangepicker.min.js"}, "Patterns": null}, "sf-timepicker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "scripts/sf-timepicker.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Charts": {"Children": {"scripts": {"Children": {"sf-accumulation-chart.min.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "scripts/sf-accumulation-chart.min.js"}, "Patterns": null}, "sf-chart.min.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "scripts/sf-chart.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Core": {"Children": {"scripts": {"Children": {"popup.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/popup.min.js"}, "Patterns": null}, "popupsbase.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/popupsbase.min.js"}, "Patterns": null}, "sf-svg-export.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/sf-svg-export.min.js"}, "Patterns": null}, "svgbase.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/svgbase.min.js"}, "Patterns": null}, "syncfusion-blazor-base.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/syncfusion-blazor-base.min.js"}, "Patterns": null}, "syncfusion-blazor.min.js": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "scripts/syncfusion-blazor.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Data": {"Children": {"scripts": {"Children": {"data.min.js": {"Children": null, "Asset": {"ContentRootIndex": 7, "SubPath": "scripts/data.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.DropDowns": {"Children": {"scripts": {"Children": {"sf-dropdownlist.min.js": {"Children": null, "Asset": {"ContentRootIndex": 8, "SubPath": "scripts/sf-dropdownlist.min.js"}, "Patterns": null}, "sf-listbox.min.js": {"Children": null, "Asset": {"ContentRootIndex": 8, "SubPath": "scripts/sf-listbox.min.js"}, "Patterns": null}, "sf-mention.min.js": {"Children": null, "Asset": {"ContentRootIndex": 8, "SubPath": "scripts/sf-mention.min.js"}, "Patterns": null}, "sf-multiselect.min.js": {"Children": null, "Asset": {"ContentRootIndex": 8, "SubPath": "scripts/sf-multiselect.min.js"}, "Patterns": null}, "sortable.min.js": {"Children": null, "Asset": {"ContentRootIndex": 8, "SubPath": "scripts/sortable.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Grid": {"Children": {"scripts": {"Children": {"sf-grid.min.js": {"Children": null, "Asset": {"ContentRootIndex": 9, "SubPath": "scripts/sf-grid.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Inputs": {"Children": {"scripts": {"Children": {"sf-colorpicker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-colorpicker.min.js"}, "Patterns": null}, "sf-maskedtextbox.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-maskedtextbox.min.js"}, "Patterns": null}, "sf-numerictextbox.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-numerictextbox.min.js"}, "Patterns": null}, "sf-otp-input.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-otp-input.min.js"}, "Patterns": null}, "sf-rating.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-rating.min.js"}, "Patterns": null}, "sf-signature.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-signature.min.js"}, "Patterns": null}, "sf-slider.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-slider.min.js"}, "Patterns": null}, "sf-speechtotext.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-speechtotext.min.js"}, "Patterns": null}, "sf-textarea.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-textarea.min.js"}, "Patterns": null}, "sf-textbox.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-textbox.min.js"}, "Patterns": null}, "sf-uploader.min.js": {"Children": null, "Asset": {"ContentRootIndex": 10, "SubPath": "scripts/sf-uploader.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Lists": {"Children": {"scripts": {"Children": {"sf-listview.min.js": {"Children": null, "Asset": {"ContentRootIndex": 11, "SubPath": "scripts/sf-listview.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Navigations": {"Children": {"scripts": {"Children": {"navigationsbase.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/navigationsbase.min.js"}, "Patterns": null}, "sf-accordion.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-accordion.min.js"}, "Patterns": null}, "sf-breadcrumb.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-breadcrumb.min.js"}, "Patterns": null}, "sf-carousel.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-carousel.min.js"}, "Patterns": null}, "sf-contextmenu.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-contextmenu.min.js"}, "Patterns": null}, "sf-dropdowntree.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-dropdowntree.min.js"}, "Patterns": null}, "sf-menu.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-menu.min.js"}, "Patterns": null}, "sf-pager.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-pager.min.js"}, "Patterns": null}, "sf-sidebar.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-sidebar.min.js"}, "Patterns": null}, "sf-stepper.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-stepper.min.js"}, "Patterns": null}, "sf-tab.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-tab.min.js"}, "Patterns": null}, "sf-toolbar.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-toolbar.min.js"}, "Patterns": null}, "sf-treeview.min.js": {"Children": null, "Asset": {"ContentRootIndex": 12, "SubPath": "scripts/sf-treeview.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Notifications": {"Children": {"scripts": {"Children": {"sf-toast.min.js": {"Children": null, "Asset": {"ContentRootIndex": 13, "SubPath": "scripts/sf-toast.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Popups": {"Children": {"scripts": {"Children": {"sf-dialog.min.js": {"Children": null, "Asset": {"ContentRootIndex": 14, "SubPath": "scripts/sf-dialog.min.js"}, "Patterns": null}, "sf-tooltip.min.js": {"Children": null, "Asset": {"ContentRootIndex": 14, "SubPath": "scripts/sf-tooltip.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.ProgressBar": {"Children": {"scripts": {"Children": {"sf-progressbar.min.js": {"Children": null, "Asset": {"ContentRootIndex": 15, "SubPath": "scripts/sf-progressbar.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.Spinner": {"Children": {"scripts": {"Children": {"sf-spinner.min.js": {"Children": null, "Asset": {"ContentRootIndex": 16, "SubPath": "scripts/sf-spinner.min.js"}, "Patterns": null}, "spinner.min.js": {"Children": null, "Asset": {"ContentRootIndex": 16, "SubPath": "scripts/spinner.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Syncfusion.Blazor.SplitButtons": {"Children": {"scripts": {"Children": {"sf-drop-down-button.min.js": {"Children": null, "Asset": {"ContentRootIndex": 17, "SubPath": "scripts/sf-drop-down-button.min.js"}, "Patterns": null}, "splitbuttonsbase.min.js": {"Children": null, "Asset": {"ContentRootIndex": 17, "SubPath": "scripts/splitbuttonsbase.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}