@page "/dashboard"
@inject IJSRuntime JSRuntime
@using Syncfusion.Blazor.Charts
@rendermode InteractiveServer

<PageTitle>Vue statistique - Wafa LLD</PageTitle>

<div class="wafa-dashboard" id="dashboard-container">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="container">
            <div class="header-content">
                <div class="company-info">
                    <h1>ATTIJARI WAFA BANK</h1>
                    <p>2, Boulevard Moulay Youssef<br/>20000 CASABLANCA</p>
                </div>
                <div class="contact-details">
                    <div class="contact-item">Tél : 0522 29 88 88</div>
                    <div class="contact-item">Fax : 0522 27 99 99</div>
                    <div class="contact-item">E-mail : <EMAIL></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
        <div class="full-width-container">
            <!-- Progress Bar KPI Cards - 7 cards in one row -->
            <div class="progress-kpi-section">
                <h2 class="section-title">Informations Générales du Parc</h2>
                <div class="progress-kpi-grid">
                    <!-- Véhicules en parc -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">VÉHICULES EN PARC</div>
                        <div class="kpi-number">62</div>
                        <div class="kpi-percentage">100%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 100%"></div>
                        </div>
                    </div>

                    <!-- Véhicules en commande -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">VÉHICULES EN COMMANDE</div>
                        <div class="kpi-number">0</div>
                        <div class="kpi-percentage">0%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- À renouveler sous 90 jours -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">À RENOUVELER SOUS 90 JOURS</div>
                        <div class="kpi-number">9</div>
                        <div class="kpi-percentage">14%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 14%"></div>
                        </div>
                    </div>

                    <!-- Restitués depuis début année -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">RESTITUÉS DEPUIS DÉBUT ANNÉE</div>
                        <div class="kpi-number">25</div>
                        <div class="kpi-percentage">40.3%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 40.3%"></div>
                        </div>
                    </div>

                    <!-- Restitués depuis 12 mois -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">RESTITUÉS DEPUIS 12 MOIS</div>
                        <div class="kpi-number">25</div>
                        <div class="kpi-percentage">40.3%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 40.3%"></div>
                        </div>
                    </div>

                    <!-- Sinistres depuis début année -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">SINISTRES DEPUIS DÉBUT ANNÉE</div>
                        <div class="kpi-number">4</div>
                        <div class="kpi-percentage">6.4%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 6.4%"></div>
                        </div>
                    </div>

                    <!-- Sinistres depuis 12 mois -->
                    <div class="kpi-card progress-card">
                        <div class="kpi-title">SINISTRES DEPUIS 12 MOIS</div>
                        <div class="kpi-number">4</div>
                        <div class="kpi-percentage">6.4%</div>
                        <div class="kpi-bar">
                            <div class="kpi-progress" style="width: 6.4%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Number-Only KPI Cards - 4 cards with different design -->
            <div class="number-kpi-section">
                <h2 class="section-title">Statistiques Moyennes</h2>
                <div class="number-kpi-grid">
                    <!-- Durée contractuelle moyenne -->
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">
                                <span class="stat-number">49.70</span>
                                <span class="stat-unit">mois</span>
                            </div>
                            <div class="stat-label">Durée contractuelle moyenne</div>
                            <div class="stat-description">Durée prévue moyenne des contrats en vie</div>
                        </div>
                    </div>

                    <!-- Age moyen du parc -->
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">
                                <span class="stat-number">30.70</span>
                                <span class="stat-unit">mois</span>
                            </div>
                            <div class="stat-label">Âge moyen du parc</div>
                            <div class="stat-description">Âge moyen du parc en vie en nombre de mois</div>
                        </div>
                    </div>

                    <!-- Kilométrage contractuel moyen -->
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">
                                <span class="stat-number">85 000</span>
                                <span class="stat-unit">km</span>
                            </div>
                            <div class="stat-label">Kilométrage contractuel moyen</div>
                            <div class="stat-description">Kilométrage prévu moyen des contrats en vie</div>
                        </div>
                    </div>

                    <!-- Kilométrage réel moyen -->
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">
                                <span class="stat-number">62 500</span>
                                <span class="stat-unit">km</span>
                            </div>
                            <div class="stat-label">Kilométrage réel moyen</div>
                            <div class="stat-description">Kilométrage réel moyen du parc en vie</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Cards Section -->
            <div class="financial-kpi-section">
                <h2 class="section-title">Informations Financières</h2>
                <div class="financial-kpi-grid">
                    <!-- Total facturé depuis début année -->
                    <div class="financial-card">
                        <div class="financial-content">
                            <div class="financial-period">Année en cours</div>
                            <div class="financial-title">Total facturé depuis le début de l'année</div>
                            <div class="financial-amounts">
                                <div class="amount-row primary">
                                    <span class="amount-label">HT</span>
                                    <span class="amount-value">5 514 742,26 €</span>
                                </div>
                                <div class="amount-row secondary">
                                    <span class="amount-label">TTC</span>
                                    <span class="amount-value-ttc">6 617 690,71 €</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total facturé depuis 12 mois -->
                    <div class="financial-card">
                        <div class="financial-content">
                            <div class="financial-period">12 derniers mois</div>
                            <div class="financial-title">Total facturé depuis 12 mois</div>
                            <div class="financial-amounts">
                                <div class="amount-row primary">
                                    <span class="amount-label">HT</span>
                                    <span class="amount-value">541 298,00 €</span>
                                </div>
                                <div class="amount-row secondary">
                                    <span class="amount-label">TTC</span>
                                    <span class="amount-value-ttc">649 557,60 €</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section - Distribution Analysis -->
            <div class="charts-section">
                <div class="container">
                    <h2 class="section-title">Répartition du Parc</h2>
            <div class="charts-grid">
                <!-- Répartition par genre -->
                <div class="chart-container">
                    <div class="chart-header">Répartition par genre</div>
                    <div class="chart-content">
                        <SfAccumulationChart Title="" Width="100%" Height="450px" EnableAnimation="true">
                            <AccumulationChartSeriesCollection>
                                <AccumulationChartSeries DataSource="@GenreData" XName="Category" YName="Value" Name="Genre" InnerRadius="0%" PointColorMapping="Color">
                                    <AccumulationDataLabelSettings Visible="true" Name="Text" Position="AccumulationLabelPosition.Outside">
                                        <AccumulationChartConnector Type="ConnectorType.Line" Length="20px"></AccumulationChartConnector>
                                    </AccumulationDataLabelSettings>
                                </AccumulationChartSeries>
                            </AccumulationChartSeriesCollection>
                            <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Bottom"></AccumulationChartLegendSettings>
                            <AccumulationChartTooltipSettings Enable="true" Format="${point.x} : <b>${point.y} (${point.percentage}%)</b>"></AccumulationChartTooltipSettings>
                        </SfAccumulationChart>
                    </div>
                </div>

                <!-- Répartition par énergie -->
                <div class="chart-container">
                    <div class="chart-header">Répartition par énergie</div>
                    <div class="chart-content">
                        <SfAccumulationChart Title="" Width="100%" Height="450px" EnableAnimation="true">
                            <AccumulationChartSeriesCollection>
                                <AccumulationChartSeries DataSource="@EnergieData" XName="Category" YName="Value" Name="Énergie" InnerRadius="0%" PointColorMapping="Color">
                                    <AccumulationDataLabelSettings Visible="true" Name="Text" Position="AccumulationLabelPosition.Outside">
                                        <AccumulationChartConnector Type="ConnectorType.Line" Length="20px"></AccumulationChartConnector>
                                    </AccumulationDataLabelSettings>
                                </AccumulationChartSeries>
                            </AccumulationChartSeriesCollection>
                            <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Bottom"></AccumulationChartLegendSettings>
                            <AccumulationChartTooltipSettings Enable="true" Format="${point.x} : <b>${point.y} (${point.percentage}%)</b>"></AccumulationChartTooltipSettings>
                        </SfAccumulationChart>
                    </div>
                </div>

                <!-- Répartition par marque -->
                <div class="chart-container">
                    <div class="chart-header">Répartition par marque</div>
                    <div class="chart-content">
                        <SfAccumulationChart Title="" Width="100%" Height="450px" EnableAnimation="true">
                            <AccumulationChartSeriesCollection>
                                <AccumulationChartSeries DataSource="@MarqueData" XName="Category" YName="Value" Name="Marque" InnerRadius="0%" PointColorMapping="Color">
                                    <AccumulationDataLabelSettings Visible="true" Name="Text" Position="AccumulationLabelPosition.Outside">
                                        <AccumulationChartConnector Type="ConnectorType.Line" Length="20px"></AccumulationChartConnector>
                                    </AccumulationDataLabelSettings>
                                </AccumulationChartSeries>
                            </AccumulationChartSeriesCollection>
                            <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Bottom"></AccumulationChartLegendSettings>
                            <AccumulationChartTooltipSettings Enable="true" Format="${point.x} : <b>${point.y} (${point.percentage}%)</b>"></AccumulationChartTooltipSettings>
                        </SfAccumulationChart>
                    </div>
                </div>

                <!-- Répartition CO2 -->
                <div class="chart-container">
                    <div class="chart-header">Répartition CO2</div>
                    <div class="chart-content">
                        <SfAccumulationChart Title="" Width="100%" Height="450px" EnableAnimation="true">
                            <AccumulationChartSeriesCollection>
                                <AccumulationChartSeries DataSource="@CO2Data" XName="Category" YName="Value" Name="CO2" InnerRadius="0%" PointColorMapping="Color">
                                    <AccumulationDataLabelSettings Visible="true" Name="Text" Position="AccumulationLabelPosition.Outside">
                                        <AccumulationChartConnector Type="ConnectorType.Line" Length="20px"></AccumulationChartConnector>
                                    </AccumulationDataLabelSettings>
                                </AccumulationChartSeries>
                            </AccumulationChartSeriesCollection>
                            <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Bottom"></AccumulationChartLegendSettings>
                            <AccumulationChartTooltipSettings Enable="true" Format="${point.x} : <b>${point.y} (${point.percentage}%)</b>"></AccumulationChartTooltipSettings>
                        </SfAccumulationChart>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Footer -->
    <div class="dashboard-footer">
        <div class="container">
            <div class="footer-content">

            </div>
        </div>


    </div>
</div>

<style>
    :root {
        --wafa-primary: #1F1F26;
        --wafa-orange: #F2A516;
        --wafa-brown: #73551D;
        --wafa-secondary: #F28729;
        --wafa-red: #D94A3D;
        --wafa-dark: #1F1F26;
        --wafa-light: #f8f9fa;
        --wafa-gray: #666;
        --wafa-border: #e0e0e0;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .wafa-dashboard {
        min-height: 100vh;
        background: #fff;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .full-width-container {
        width: 100%;
        padding: 0 20px;
    }

    /* Header Section */
    .dashboard-header {
        background: var(--wafa-light);
        border-bottom: 1px solid var(--wafa-border);
        padding: 30px 0;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 30px;
    }

    .company-info h1 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--wafa-dark);
        margin-bottom: 10px;
    }

    .company-info p {
        color: var(--wafa-gray);
        font-size: 14px;
        line-height: 1.5;
    }

    .contact-details {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .contact-item {
        color: var(--wafa-gray);
        font-size: 14px;
    }

    /* Dashboard Content */
    .dashboard-content {
        padding: 40px 0;
    }

    /* Section Titles */
    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--wafa-dark);
        margin-bottom: 20px;
        padding-left: 10px;
        border-left: 4px solid var(--wafa-orange);
    }

    /* Progress KPI Section - 7 cards in one row */
    .progress-kpi-section {
        margin-bottom: 50px;
    }

    .progress-kpi-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 12px;
        margin-bottom: 20px;
    }

    .kpi-card.progress-card {
        background: #fff;
        border: 1px solid var(--wafa-border);
        border-radius: 6px;
        padding: 15px;
        text-align: center;
        transition: box-shadow 0.3s ease;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .kpi-card.progress-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .progress-card .kpi-title {
        font-size: 10px;
        font-weight: 600;
        color: var(--wafa-gray);
        text-transform: uppercase;
        letter-spacing: 0.3px;
        margin-bottom: 10px;
        line-height: 1.2;
    }

    .progress-card .kpi-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--wafa-orange);
        margin-bottom: 5px;
    }

    .progress-card .kpi-percentage {
        font-size: 12px;
        font-weight: 600;
        color: var(--wafa-dark);
        margin-bottom: 8px;
    }

    .kpi-bar {
        width: 100%;
        height: 4px;
        background: var(--wafa-border);
        border-radius: 2px;
        overflow: hidden;
        margin-top: auto;
    }

    .kpi-progress {
        height: 100%;
        background: linear-gradient(90deg, var(--wafa-orange), var(--wafa-secondary));
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    /* Number KPI Section - 4 cards with different design */
    .number-kpi-section {
        margin-bottom: 40px;
    }

    .number-kpi-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }

    /* Stat Cards (Number-Only) */
    .stat-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        min-height: 160px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .stat-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
        border-color: var(--wafa-orange);
    }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .stat-icon-bg {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: bold;
        color: white;
    }

    .stat-icon-bg.duration { background: linear-gradient(135deg, var(--wafa-orange), var(--wafa-secondary)); }
    .stat-icon-bg.age { background: linear-gradient(135deg, var(--wafa-brown), var(--wafa-secondary)); }
    .stat-icon-bg.distance { background: linear-gradient(135deg, var(--wafa-red), var(--wafa-secondary)); }
    .stat-icon-bg.actual { background: linear-gradient(135deg, var(--wafa-primary), var(--wafa-brown)); }

    .stat-trend {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
    }

    .stat-trend.positive { background: rgba(242, 165, 22, 0.1); color: var(--wafa-orange); }
    .stat-trend.negative { background: rgba(217, 74, 61, 0.1); color: var(--wafa-red); }
    .stat-trend.neutral { background: rgba(115, 85, 29, 0.1); color: var(--wafa-brown); }

    .stat-content {
        text-align: left;
    }

    .stat-value {
        margin-bottom: 12px;
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--wafa-dark);
        line-height: 1;
    }

    .stat-unit {
        font-size: 1rem;
        color: var(--wafa-gray);
        font-weight: 500;
        margin-left: 8px;
    }

    .stat-label {
        font-size: 11px;
        font-weight: 600;
        color: var(--wafa-dark);
        margin-bottom: 4px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-description {
        font-size: 10px;
        color: var(--wafa-gray);
        line-height: 1.4;
    }

    /* Financial KPI Section */
    .financial-kpi-section {
        margin-bottom: 40px;
    }

    .financial-kpi-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    /* Financial Cards */
    .financial-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        min-height: 180px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .financial-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
        border-color: var(--wafa-orange);
    }

    .financial-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .financial-icon-bg {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        color: white;
    }

    .financial-icon-bg.yearly { background: linear-gradient(135deg, var(--wafa-orange), var(--wafa-secondary)); }
    .financial-icon-bg.monthly { background: linear-gradient(135deg, var(--wafa-brown), var(--wafa-red)); }

    .financial-period {
        padding: 8px 16px;
        background: rgba(242, 165, 22, 0.1);
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        color: var(--wafa-orange);
        text-transform: uppercase;
        margin-bottom: 12px;
        display: inline-block;
        border: 1px solid rgba(242, 165, 22, 0.2);
    }

    .financial-content {
        text-align: left;
    }

    .financial-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--wafa-dark);
        margin-bottom: 16px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .financial-amounts {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .amount-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        border-left: 4px solid transparent;
    }

    .amount-row.primary {
        background: rgba(242, 165, 22, 0.1);
        border-left-color: var(--wafa-orange);
    }

    .amount-row.secondary {
        background: rgba(115, 85, 29, 0.1);
        border-left-color: var(--wafa-brown);
    }

    .amount-label {
        font-size: 12px;
        font-weight: 600;
        color: var(--wafa-gray);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .amount-value {
        font-size: 16px;
        font-weight: 700;
        color: var(--wafa-dark);
    }

    .amount-value-ttc {
        font-size: 16px;
        font-weight: 700;
        color: var(--wafa-brown);
    }

    /* Footer */
    .dashboard-footer {
        background: var(--wafa-light);
        border-top: 1px solid var(--wafa-border);
        padding: 20px 0;
        margin-top: 40px;
    }

    .footer-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .footer-left p,
    .footer-right p {
        color: var(--wafa-gray);
        font-size: 14px;
        margin: 0;
    }

    /* Responsive Design */
    @@media (max-width: 1400px) {
        .progress-kpi-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .number-kpi-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .financial-kpi-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
    }

    @@media (max-width: 1200px) {
        .progress-kpi-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .number-kpi-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .financial-kpi-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
    }

    @@media (max-width: 768px) {
        .header-content {
            flex-direction: column;
            text-align: center;
        }

        .company-info h1 {
            font-size: 1.5rem;
        }

        .contact-details {
            align-items: center;
        }

        .full-width-container {
            padding: 0 15px;
        }

        .progress-kpi-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .number-kpi-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .financial-kpi-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .kpi-card.progress-card {
            padding: 12px;
            min-height: 100px;
        }

        .progress-card .kpi-title {
            font-size: 10px;
        }

        .progress-card .kpi-number {
            font-size: 1.5rem;
        }

        .stat-card {
            padding: 20px;
            min-height: 160px;
        }

        .stat-number {
            font-size: 1.6rem;
        }

        .financial-card {
            padding: 20px;
            min-height: 180px;
        }

        .amount-value,
        .amount-value-ttc {
            font-size: 14px;
        }

        .footer-content {
            flex-direction: column;
            text-align: center;
        }
    }

    @@media (max-width: 480px) {
        .progress-kpi-grid {
            grid-template-columns: 1fr;
        }

        .number-kpi-grid {
            grid-template-columns: 1fr;
        }

        .financial-kpi-grid {
            grid-template-columns: 1fr;
        }

        .kpi-card.progress-card {
            padding: 15px;
            min-height: 120px;
        }

        .progress-card .kpi-number {
            font-size: 1.6rem;
        }

        .stat-card {
            padding: 20px;
        }

        .stat-number {
            font-size: 1.4rem;
        }

        .financial-card {
            padding: 18px;
            min-height: 170px;
        }

        .amount-value,
        .amount-value-ttc {
            font-size: 13px;
        }

        .section-title {
            font-size: 1rem;
        }

        /* Mobile-specific improvements */
        .dashboard-header {
            padding: 20px 0;
        }

        .dashboard-content {
            padding: 20px 0;
        }

        /* Better touch targets for mobile */
        .kpi-card.progress-card,
        .stat-card,
        .financial-card {
            cursor: default;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* Improve scrolling on mobile */
        .main-content {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* Ensure content doesn't go under potential notches and floating button */
        .full-width-container {
            padding: 0 max(15px, env(safe-area-inset-left)) 0 max(15px, env(safe-area-inset-right));
        }

        /* Add top margin to avoid floating hamburger button on mobile only */
        .dashboard-header {
            margin-top: 80px;
        }
    }

    /* Desktop styles - remove mobile-specific margins */
    @@media (min-width: 769px) {
        .dashboard-header {
            margin-top: 0 !important;
        }
    }

    /* Enhanced transition animations */
    .wafa-dashboard {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dashboard-content {
        transition: padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Mode transition indicator */
    .mode-transition-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .mode-transition-overlay.active {
        opacity: 1;
        pointer-events: auto;
    }

    .transition-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--wafa-orange);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    /* Charts Section Styling */
    .charts-section {
        margin-top: 40px;
        padding: 0 20px;
    }



    .charts-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
        margin-top: 25px;
    }

    .chart-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .chart-container:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #F6B614;
    }

    .chart-header {
        background: white;
        color: #333;
        padding: 15px 20px;
        font-weight: 600;
        font-size: 16px;
        text-align: center;
        border-bottom: 2px solid #F6B614;
        position: relative;
    }

    .chart-content {
        padding: 15px;
        background: white;
        position: relative;
    }

    /* Mobile responsive charts */
    @@media (max-width: 768px) {
        .charts-section {
            padding: 0 10px;
            margin-top: 25px;
        }

        .charts-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .chart-header {
            font-size: 14px;
            padding: 12px 15px;
        }

        .chart-content {
            padding: 10px;
        }

        .charts-section .section-title {
            font-size: 20px;
            margin-bottom: 20px;
        }
    }
</style>

@code {
    private bool IsTransitioning = false;

    // Chart data sources
    private List<ChartData> GenreData = new();
    private List<ChartData> EnergieData = new();
    private List<ChartData> MarqueData = new();
    private List<ChartData> CO2Data = new();



    protected override void OnInitialized()
    {
        InitializeChartData();
    }

    private void InitializeChartData()
    {
        // Répartition par genre (using creative color palette)
        GenreData = new List<ChartData>
        {
            new ChartData { Category = "Véhicules Particuliers", Value = 85, Text = "85 (85%)", Color = "#F6B614" }, // Bright Yellow
            new ChartData { Category = "Véhicules utilitaires", Value = 15, Text = "15 (15%)", Color = "#EF8C29" }   // Orange
        };

        // Répartition par énergie (using creative color palette)
        EnergieData = new List<ChartData>
        {
            new ChartData { Category = "Gasoil", Value = 65, Text = "65 (65%)", Color = "#D94A3D" },      // Red
            new ChartData { Category = "Essence", Value = 25, Text = "25 (25%)", Color = "#F6B614" },     // Yellow
            new ChartData { Category = "Électrique", Value = 10, Text = "10 (10%)", Color = "#1F1F26" }   // Dark
        };

        // Répartition par marque (using creative color palette)
        MarqueData = new List<ChartData>
        {
            new ChartData { Category = "Renault", Value = 40, Text = "40 (40%)", Color = "#EF8C29" },     // Orange
            new ChartData { Category = "Peugeot", Value = 30, Text = "30 (30%)", Color = "#F6B614" },     // Yellow
            new ChartData { Category = "Citroën", Value = 20, Text = "20 (20%)", Color = "#D94A3D" },     // Red
            new ChartData { Category = "Autres", Value = 10, Text = "10 (10%)", Color = "#1F1F26" }       // Dark
        };

        // Répartition CO2 (using creative color palette)
        CO2Data = new List<ChartData>
        {
            new ChartData { Category = "Faible (0-120g)", Value = 30, Text = "30 (30%)", Color = "#F6B614" },    // Yellow
            new ChartData { Category = "Moyen (121-160g)", Value = 45, Text = "45 (45%)", Color = "#EF8C29" },   // Orange
            new ChartData { Category = "Élevé (161g+)", Value = 25, Text = "25 (25%)", Color = "#D94A3D" }       // Red
        };
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Listen for mode change events
            await JSRuntime.InvokeVoidAsync("eval", @"
                window.addEventListener('modeChanged', function(event) {
                    console.log('Dashboard: Mode changed to', event.detail.isMobile ? 'mobile' : 'desktop');
                    // Trigger any dashboard-specific adjustments
                    document.getElementById('dashboard-container').classList.toggle('mobile-mode', event.detail.isMobile);
                });

                window.addEventListener('sidebarToggled', function(event) {
                    console.log('Dashboard: Sidebar toggled', event.detail);
                    // Adjust dashboard layout based on sidebar state
                    document.getElementById('dashboard-container').classList.toggle('sidebar-closed', !event.detail.isOpen);
                });
            ");
        }
    }

    // Chart data model
    public class ChartData
    {
        public string Category { get; set; } = "";
        public int Value { get; set; }
        public string Text { get; set; } = "";
        public string Color { get; set; } = "";
    }
}


