/* _content/CWR_App/Components/Layout/MainLayout.razor.rz.scp.css */
.wafa-app[b-anuzff0wnj] {
    display: flex;
    height: 100vh;
    font-family: Arial, sans-serif;
    background-color: #f8f9fa;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-container[b-anuzff0wnj] {
    flex-shrink: 0;
    position: relative;
    z-index: 1000;
}

.main-content[b-anuzff0wnj] {
    flex: 1;
    height: 100vh;
    overflow-y: auto;
    background: #f8f9fa;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Override Syncfusion Sidebar to work with flexbox */
[b-anuzff0wnj] .e-sidebar {
    height: 100vh !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
}

[b-anuzff0wnj] .e-sidebar-context {
    height: 100vh !important;
}

/* Remove Syncfusion's default positioning */
[b-anuzff0wnj] .e-main-content {
    position: static !important;
    margin-left: 0 !important;
    width: 100% !important;
}

/* Desktop styles - ensure normal behavior */
@media (min-width: 769px) {
    .wafa-app[b-anuzff0wnj] {
        display: flex !important;
        flex-direction: row !important;
    }

    .sidebar-container[b-anuzff0wnj] {
        position: relative !important;
        flex-shrink: 0 !important;
        width: auto !important;
        height: 100vh !important;
        z-index: 1000 !important;
    }

    [b-anuzff0wnj] .e-sidebar {
        position: relative !important;
        z-index: auto !important;
        height: 100vh !important;
        display: block !important;
    }

    .main-content[b-anuzff0wnj] {
        flex: 1 !important;
        margin-left: 0 !important;
        padding-top: 0 !important;
        height: 100vh !important;
        overflow-y: auto !important;
    }
}

@media (max-width: 768px) {
    .wafa-app[b-anuzff0wnj] {
        flex-direction: column;
    }

    .sidebar-container[b-anuzff0wnj] {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1001;
        width: auto;
    }

    .main-content[b-anuzff0wnj] {
        width: 100%;
        margin-left: 0;
        padding-top: 0; /* Remove padding since sidebar is overlay */
    }

    [b-anuzff0wnj] .e-sidebar {
        position: fixed !important;
        z-index: 1001 !important;
        top: 0 !important;
        left: 0 !important;
        height: 100vh !important;
    }

    /* Ensure sidebar container is properly positioned */
    .sidebar-container[b-anuzff0wnj] {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1002;
        width: auto;
        height: auto;
    }
}

#blazor-error-ui[b-anuzff0wnj] {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss[b-anuzff0wnj] {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

/* Global mode transition styles */
.mode-transition-overlay[b-anuzff0wnj] {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.mode-transition-overlay.active[b-anuzff0wnj] {
    opacity: 1;
    pointer-events: auto;
}

.transition-spinner[b-anuzff0wnj] {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6b35;
    border-radius: 50%;
    animation: spin-b-anuzff0wnj 1s linear infinite;
}

@keyframes spin-b-anuzff0wnj {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Global mode-specific styles */
body.mobile-mode[b-anuzff0wnj] {
    --content-padding: 15px;
    --font-scale: 1.1;
}

body.desktop-mode[b-anuzff0wnj] {
    --content-padding: 30px;
    --font-scale: 1;
}
