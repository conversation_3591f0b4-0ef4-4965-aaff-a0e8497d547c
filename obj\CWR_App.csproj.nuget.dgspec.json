{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\CWR_App.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\CWR_App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\CWR_App.csproj", "projectName": "CWR_App", "projectPath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\CWR_App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Mabchour\\CWR_App\\CWR_App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Syncfusion.Blazor.Charts": {"target": "Package", "version": "[30.1.41, )"}, "Syncfusion.Blazor.Grid": {"target": "Package", "version": "[30.1.41, )"}, "Syncfusion.Blazor.ProgressBar": {"target": "Package", "version": "[30.1.41, )"}, "Syncfusion.Blazor.Themes": {"target": "Package", "version": "[30.1.41, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}